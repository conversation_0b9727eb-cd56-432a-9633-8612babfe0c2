import websocket from './utils/websocket';
import { loginManager } from './utils/loginManager';
import eventBus from './utils/eventBus';

App({
  globalData: {
    wangz: 'http://localhost:80/index.php',
    // wangz: 'https://www.bjgaoxiaoshequ.store',
    username: '点击此处登录',
    face_url: 'https://www.bjgaoxiaoshequ.store/images/weixiao.png',
    user_id: '',
    phone: '',
    isLoggedIn: false,
    status: '',
    unread: '',
    unreadComments: 0,  // 添加未读评论数
    unreadReplies: 0,   // 添加未读回复数
    unreadActivityCount: 0, // 添加未读活动数
    activitySubscribed: true, // 默认订阅活动
    showOfficialAccountTip: true, // 默认显示公众号提示
    showHotTopic: true, // 默认显示每日热榜
    titlename: '无',
    openid: '',
    access_token: '',
    refresh_token: '',
    websocket: websocket,
    isLoginReady: false,
    loginManager: loginManager,
    loginListeners: [],
    eventBus: eventBus,
    updateLikeNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      return new Promise((resolve) => {
        wx.request({
          url: `${this.wangz}/notification/getList`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            user_id: this.user_id,
            type: 'like',
            show_all: 0
          },
          success: (res) => {
            if (res.data.error_code === 0) {
              const notifications = res.data.data.list || [];
              const likesCount = notifications.filter(n => n.type === 'like' && !n.is_read).length;
              this.unread = likesCount;
              resolve();
            } else {
              resolve();
            }
          },
          fail: (error) => {
            resolve();
          }
        });
      });
    },
    updateCommentNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      return new Promise((resolve) => {
        wx.request({
          url: `${this.wangz}/notification/getList`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            user_id: this.user_id,
            type: 'comment',
            show_all: false
          },
          success: (res) => {
            if (res.data.error_code === 0) {
              const notifications = res.data.data.list || [];
              const commentsCount = notifications.filter(n => !n.is_read).length;
              this.unreadComments = commentsCount;
              resolve();
            } else {
              resolve();
            }
          },
          fail: (error) => {
            resolve();
          }
        });
      });
    },
    updateReplyNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      return new Promise((resolve) => {
        wx.request({
          url: `${this.wangz}/notification/getList`,
          method: 'POST',
          header: {
            'content-type': 'application/x-www-form-urlencoded'
          },
          data: {
            user_id: this.user_id,
            type: 'reply',
            show_all: 0
          },
          success: (res) => {
            if (res.data.error_code === 0) {
              const notifications = res.data.data.list || [];
              const repliesCount = notifications.filter(n => n.type === 'reply' && !n.is_read).length;
              this.unreadReplies = repliesCount;
              resolve();
            } else {
              resolve();
            }
          },
          fail: () => {
            resolve();
          }
        });
      });
    },
    async updateAllNotifications() {
      if (!this.user_id || !this.isLoggedIn) return;

      try {
        // 并行获取所有类型的通知
        await Promise.all([
          this.updateLikeNotifications(),
          this.updateCommentNotifications(),
          this.updateReplyNotifications()
        ]);

        // 计算新的通知总数
        const newTotal = parseInt(this.unread || 0) +
                        parseInt(this.unreadComments || 0) +
                        parseInt(this.unreadReplies || 0);

        // 获取当前页面实例
        const pages = getCurrentPages();
        const mePage = pages.find(page => page.route === 'pages/fold3/me/me');

        // 如果个人中心页面存在，更新其通知数量
        if (mePage) {
          mePage.updateGridNotifications();
        }

        // 检查当前是否在TabBar页面
        const currentPage = pages[pages.length - 1];
        const isTabBarPage = currentPage &&
          ['pages/fold1/home/<USER>', 'pages/fold2/xuqiu/xuqiu',
           'pages/fold3/me/me', 'pages/fold4/gongju/gongju'].includes(currentPage.route);

        // 只在TabBar页面上才设置徽章
        if (isTabBarPage && newTotal > 0) {
          wx.setTabBarBadge({
            index: 3,
            text: newTotal.toString()
          }).catch(() => {});
        }
      } catch (error) {
        // 保留错误处理但不打印日志
      }
    },
    addLoginListener(callback) {
      this.loginListeners.push(callback);
    },
    removeLoginListener(callback) {
      const index = this.loginListeners.indexOf(callback);
      if (index > -1) {
        this.loginListeners.splice(index, 1);
      }
    },
    // 初始化通知相关变量
    initNotifications() {
      this.unread = 0;
      this.unreadComments = 0;
      this.unreadReplies = 0;
      this.lastTotalCount = 0;
      this.unreadActivityCount = 0;
    },
    // 清除活动相关的所有红点和角标
    clearActivityBadge() {
      // 清除未读计数并移除TabBar红点
      this.clearActivityNotifications();

      // 尝试更新工具页中活动专区按钮的badge
      try {
        // 获取当前页面栈
        const pages = getCurrentPages();
        // 找到工具页面
        const gongjuPage = pages.find(page => page.route === 'pages/fold4/gongju/gongju');

        // 如果找到工具页面，清除其角标
        if (gongjuPage) {
          const grid4 = gongjuPage.data.grid4;
          if (grid4 && grid4.length > 2) {
            grid4[2].badge = 0; // 活动专区在grid4中的索引为2
            gongjuPage.setData({ grid4 });
          }
        }
      } catch (e) {
        console.error('清除活动专区badge失败:', e);
      }
    },
    // 检查新活动通知
    checkNewActivities() {
      // 获取用户ID
      const userId = this.user_id || '';

      // 如果用户ID为空，不进行请求
      if (!userId) {
        console.log('用户ID为空，跳过新活动检查');
        return;
      }

      // 从本地存储获取订阅状态
      const isSubscribed = wx.getStorageSync('activitySubscribe');
      // 如果没有设置，默认为true
      const subscribed = isSubscribed !== "" ? isSubscribed : true;

      // 更新全局变量
      this.activitySubscribed = subscribed;

      // 如果用户未订阅，不显示红点
      if (!subscribed) {
        this.clearActivityNotifications();
        return;
      }

      console.log('检查新活动, userId:', userId);

      wx.request({
        url: this.wangz + '/activity/getNewActivityCount',
        method: 'POST',
        header: {
          'content-type': 'application/x-www-form-urlencoded',
          'Authorization': wx.getStorageSync('access_token')
        },
        data: {
          user_id: userId,
          subscribed: subscribed ? '1' : '0'
        },
        success: (res) => {
          console.log('新活动检查响应:', res.data);

          if (res.data.code === 200) {
            const count = res.data.data.count;

            // 更新全局状态
            this.unreadActivityCount = count;

            // 保存到本地存储
            wx.setStorageSync('unreadActivityCount', count);

            // 如果服务器返回了最后查看时间，更新本地缓存
            if (res.data.data.last_view_time) {
              wx.setStorageSync('activityLastViewTime', res.data.data.last_view_time);
            }

            // 根据未读计数更新红点
            this.updateActivityBadge(count);
          } else {
            // 请求失败也清除红点
            this.clearActivityNotifications();
          }
        },
        fail: (err) => {
          console.error('检查新活动失败:', err);
          // 请求失败清除红点
          this.clearActivityNotifications();
        }
      });
    },

    // 根据未读计数更新活动红点
    updateActivityBadge(count) {
      if (count > 0) {
        console.log(`显示红点：有${count}个新活动`);

        // 设置TabBar右上角的数字
        wx.setTabBarBadge({
          index: 2, // 工具页是第3个tab（索引为2）
          text: count.toString() // 转为字符串
        }).catch(() => {
          // 忽略可能的错误
        });
      } else {
        console.log('移除红点：没有新活动');

        // 移除TabBar上的数字红点
        wx.removeTabBarBadge({
          index: 2
        }).catch(() => {
          // 忽略可能的错误
        });
      }
    },

    // 清除活动通知
    clearActivityNotifications() {
      // 确保TabBar没有红点
      wx.removeTabBarBadge({
        index: 2
      }).catch(() => {
        // 忽略可能的错误
      });
      // 清除未读计数
      this.unreadActivityCount = 0;
      wx.setStorageSync('unreadActivityCount', 0);
    },
  },

  onLaunch() {
    // 从本地存储读取状态
    const activitySubscribed = wx.getStorageSync('activitySubscribe');
    const showOfficialAccountTip = wx.getStorageSync('showOfficialAccountTip');
    const showHotTopic = wx.getStorageSync('showHotTopic');

    if (activitySubscribed !== "") {
      this.globalData.activitySubscribed = activitySubscribed;
    }

    if (showOfficialAccountTip !== "") {
      this.globalData.showOfficialAccountTip = showOfficialAccountTip;
    }

    if (showHotTopic !== "") {
      this.globalData.showHotTopic = showHotTopic;
    }

    // 初始化通知相关变量
    this.globalData.initNotifications();

    // 初始化活动订阅状态
    if (wx.getStorageSync('activitySubscribe') === '') {
      // 如果从未设置过，默认为true
      wx.setStorageSync('activitySubscribe', true);
    }

    // 将订阅状态保存到全局变量
    this.globalData.activitySubscribed = wx.getStorageSync('activitySubscribe');

    // 初始化loginManager
    loginManager.init(this).then(() => {
      // 启动时立即执行自动登录
      return loginManager.autoLogin();
    }).then((loginSuccess) => {
      this.globalData.isLoginReady = true;
      // 通知所有监听器登录已就绪
      this.globalData.loginListeners.forEach(callback => callback(loginSuccess));

      // 只有在登录成功且有user_id的情况下才初始化WebSocket
      // if (loginSuccess && this.globalData.user_id) {
      //   console.log('[WebSocket] 初始化，userId:', this.globalData.user_id);
      //   this.globalData.websocket.init(this.globalData.user_id);
      // } else {
      //   console.log('[WebSocket] 初始化失败：未登录或无userId');
      // }
    }).catch(error => {
      console.error('[App] 自动登录失败:', error);
      this.globalData.isLoginReady = true;
      this.globalData.loginListeners.forEach(callback => callback(false));
    });

    // 加载字体
    wx.loadFontFace({
      family: '阿里妈妈东方大楷 Regular',
      global: true,
      source: 'url("https://www.bjgaoxiaoshequ.store/阿里妈妈东方大楷/Ar.woff2")',
    });
    wx.loadFontFace({
      family: '阿里妈妈刀隶体 Regular',
      global: true,
      source: 'url("https://www.bjgaoxiaoshequ.store/阿里妈妈刀隶体/At.woff2")'
    });

    // 监听网络状态
    wx.onNetworkStatusChange((res) => {
      if (res.isConnected && !this.globalData.isLoggedIn) {
        console.log('[登录] 网络重新连接，尝试重新登录');
        loginManager.login(true).catch(error => {
          console.error('[App] 重连登录失败:', error);
        });
      }
    });

    // 添加请求拦截器
    const originalRequest = wx.request;
    Object.defineProperty(wx, 'request', {
      configurable: true,
      enumerable: true,
      writable: true,
      value: (options) => {
        // 自动添加 user_id 到请求数据中
        if (options.data && typeof options.data === 'object') {
          options.data.user_id = this.globalData.user_id;
        }
        return originalRequest.call(wx, options);
      }
    });

    // 重写navigateTo方法，处理页面跳转逻辑
    const originalNavigateTo = wx.navigateTo;
    Object.defineProperty(wx, 'navigateTo', {
      configurable: true,
      enumerable: true,
      writable: true,
      value: (options) => {
        const url = options.url;
        const app = this;

        // 如果跳转到"点赞我的"页面，先发送已读请求
        if (url.includes('likes') && !url.includes('myLikes') && this.globalData.user_id && this.globalData.isLoggedIn) {
          wx.request({
            url: `${this.globalData.wangz}/notification/markRead`,
            method: 'POST',
            header: {
              'content-type': 'application/x-www-form-urlencoded'
            },
            data: {
              user_id: this.globalData.user_id,
              type: 'like'
            },
            success: (res) => {
              if (res.data.error_code === 0) {
                this.globalData.unread = 0;

                // 不再设置本地存储标记
                // 直接更新UI显示
                try {
                  // 尝试找到并更新我的页面
                  const pages = getCurrentPages();
                  const mePage = pages.find(page => page.route === 'pages/fold3/me/me');
                  if (mePage && mePage.updateGridNotifications) {
                    mePage.updateGridNotifications();
                  }
                } catch (e) {
                  // 错误处理
                }
              }
            },
            fail: (err) => {
              // 错误处理
            },
            complete: () => {
              // 无论请求成功还是失败，都继续页面跳转
              originalNavigateTo.call(wx, options);
            }
          });
        }
        // 如果跳转到"评论我的"页面，清除评论和回复的未读通知
        else if (url.includes('reply') && this.globalData.user_id && this.globalData.isLoggedIn) {
          // 使用Promise.all处理两个请求
          Promise.all([
            // 清除评论未读通知
            new Promise((resolve) => {
              wx.request({
                url: `${this.globalData.wangz}/notification/markRead`,
                method: 'POST',
                header: {
                  'content-type': 'application/x-www-form-urlencoded'
                },
                data: {
                  user_id: this.globalData.user_id,
                  type: 'comment'
                },
                success: (res) => {
                  if (res.data.error_code === 0) {
                    this.globalData.unreadComments = 0;
                  }
                  resolve();
                },
                fail: (err) => {
                  resolve();
                }
              });
            }),
            // 清除回复未读通知
            new Promise((resolve) => {
              wx.request({
                url: `${this.globalData.wangz}/notification/markRead`,
                method: 'POST',
                header: {
                  'content-type': 'application/x-www-form-urlencoded'
                },
                data: {
                  user_id: this.globalData.user_id,
                  type: 'reply'
                },
                success: (res) => {
                  if (res.data.error_code === 0) {
                    this.globalData.unreadReplies = 0;
                  }
                  resolve();
                },
                fail: (err) => {
                  resolve();
                }
              });
            })
          ]).then(() => {
            // 更新TabBar和通知显示
            this.globalData.updateAllNotifications();

            // 完成后跳转页面
            originalNavigateTo.call(wx, options);
          }).catch((err) => {
            // 发生错误时也继续跳转
            originalNavigateTo.call(wx, options);
          });
        } else {
          // 其他页面正常跳转
          originalNavigateTo.call(wx, options);
        }
      }
    });
  },

  // 检查登录状态的方法
  checkLoginReady() {
    return new Promise((resolve) => {
      if (this.globalData.isLoginReady) {
        resolve(this.globalData.isLoggedIn);
      } else {
        const callback = (loginSuccess) => {
          this.globalData.removeLoginListener(callback);
          resolve(loginSuccess);
        };
        this.globalData.addLoginListener(callback);
      }
    });
  },

  // 发送WebSocket消息
  sendWebSocketMessage(data) {
    if (this.globalData.websocket) {
      return this.globalData.websocket.send(data);
    }
    return Promise.reject(new Error('WebSocket not initialized'));
  },

  // 关闭WebSocket连接
  closeWebSocket() {
    if (this.globalData.websocket) {
      this.globalData.websocket.close();
    }
  },

  onHide() {
    // 进入后台时关闭WebSocket连接
    this.closeWebSocket();
  },

  onShow() {
    // 只在用户已登录时检查新活动
    if (this.globalData.isLoggedIn && this.globalData.user_id) {
      // 检查新活动
      this.globalData.checkNewActivities();
    } else {
      console.log('用户未登录，跳过活动检查');
    }

    // 回到前台时，如果已登录则重新连接WebSocket并更新通知
    if (this.globalData.isLoggedIn && this.globalData.access_token) {
      this.globalData.websocket.reconnect();
      this.globalData.updateAllNotifications();  // 添加更新所有通知的调用
    }
  }
});