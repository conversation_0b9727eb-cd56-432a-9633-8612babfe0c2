// components/anonymous-comment/anonymous-comment.js
Component({
  properties: {
    // 评论数据
    commentData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal) {
          this.processAnonymousComment(newVal);
        }
      }
    },
    // 是否来自匿名帖子
    isFromAnonymousPost: {
      type: <PERSON><PERSON>an,
      value: false
    },
    // 原帖作者的真实user_id（用于判断是否是帖主）
    originalPosterId: {
      type: Number,
      value: 0
    },
    // 当前用户信息
    currentUser: {
      type: Object,
      value: {}
    }
  },

  data: {
    processedComment: {},
    isOriginalPoster: false,
    expandedReplies: 3, // 默认显示3条回复
    defaultRepliesShow: 3,
    highlightedReplyId: 0,
    longPressReplyId: 0
  },

  methods: {
    // 处理匿名评论数据
    processAnonymousComment(commentData) {
      const processedComment = { ...commentData };
      
      // 检查是否是原帖作者
      const isOriginalPoster = this.checkIsOriginalPoster(commentData);
      
      // 如果是匿名帖子，设置匿名显示
      if (this.data.isFromAnonymousPost) {
        processedComment.displayTitle = isOriginalPoster ? '帖主' : '';
        processedComment.isAnonymousDisplay = true;
        // 头像URL由后端提供，不需要前端生成
      }

      this.setData({
        processedComment: processedComment,
        isOriginalPoster: isOriginalPoster
      });
    },

    // 检查是否是原帖作者
    checkIsOriginalPoster(commentData) {
      // 在匿名帖子中，通过比较真实的user_id来判断是否是帖主
      // 注意：这个判断应该在后端完成，前端只接收处理后的数据
      return commentData.is_original_poster || false;
    },

    // 注：头像URL由后端提供，前端不再生成头像

    // 处理回复点击
    onReplyClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('reply', {
        commentData: this.data.processedComment
      });
    },

    // 处理点赞
    onLikeClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('like', {
        commentData: this.data.processedComment
      });
    },

    // 处理图片点击
    onImageClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const { url, index } = e.currentTarget.dataset;
      this.triggerEvent('imageclick', {
        url: url,
        index: index,
        images: this.data.processedComment.images || []
      });
    },

    // 处理长按
    onLongPress(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('longpress', {
        commentData: this.data.processedComment
      });
    },

    // 处理回复的回复点击
    onReplyToReplyClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const reply = e.currentTarget.dataset.reply;
      this.triggerEvent('replyToReply', {
        commentData: this.data.processedComment,
        replyData: reply
      });
    },

    // 处理回复长按
    onReplyLongPress(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const reply = e.currentTarget.dataset.reply;
      this.triggerEvent('replyLongPress', {
        commentData: this.data.processedComment,
        replyData: reply
      });
    },

    // 处理回复点赞
    onReplyLike(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const reply = e.currentTarget.dataset.reply;
      this.triggerEvent('replyLike', {
        commentData: this.data.processedComment,
        replyData: reply
      });
    },

    // 处理回复图片点击
    onReplyImageClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const { url, images } = e.currentTarget.dataset;
      this.triggerEvent('replyImageClick', {
        url: url,
        images: images
      });
    },

    // 展开/收起回复
    toggleReplies(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const currentExpanded = this.data.expandedReplies;
      const totalReplies = this.data.processedComment.replies ? this.data.processedComment.replies.length : 0;

      if (currentExpanded < totalReplies) {
        // 展开所有回复
        this.setData({
          expandedReplies: totalReplies
        });
      } else {
        // 收起到默认数量
        this.setData({
          expandedReplies: this.data.defaultRepliesShow
        });
      }
    }
  }
});
