<!-- components/anonymous-comment/anonymous-comment.wxml -->
<view class="anonymous-comment-container {{processedComment.isAnonymousDisplay ? 'anonymous-style' : 'normal-style'}}"
      bindtap="onReplyClick"
      bindlongpress="onLongPress">

  <!-- 用户信息区域 - 完全使用原本的touxiang1结构 -->
  <view class="comment-user-info touxiang1" style="display: flex; align-items: center;">
    <image src="{{processedComment.face_url}}"
           class="{{processedComment.isAnonymousDisplay ? 'anonymous-avatar' : 'normal-avatar'}}"
           style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
    <view style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
      <view style="display: flex; align-items: center;">
        <text class="username {{processedComment.isAnonymousDisplay ? 'anonymous-username' : 'normal-username'}}"
              style="margin-left:15rpx;">
          {{processedComment.isAnonymousDisplay ? (processedComment.anonymous_name || processedComment.username) : processedComment.username}}
        </text>

        <!-- 头衔显示 -->
        <text wx:if="{{processedComment.displayTitle}}"
              class="title-badge {{processedComment.isAnonymousDisplay ? 'anonymous-title' : 'normal-title'}}">
          {{processedComment.displayTitle}}
        </text>
        <text wx:elif="{{!processedComment.isAnonymousDisplay && processedComment.titlename && processedComment.titlename !== '无' && processedComment.titlename !== 'undefined'}}"
              class="title-badge normal-title bg{{processedComment.titlecolor}}">
          {{processedComment.titlename}}
        </text>
      </view>

      <!-- 时间显示移到用户名下方 -->
      <view style="margin-left:15rpx; margin-top: 5rpx;">
        <text class="comment-time">{{processedComment.time_display}}</text>
      </view>
    </view>
  </view>

  <!-- 评论内容 - 完全使用原本的结构 -->
  <view class="comment-content" bindtap="onReplyClick"
        style="-webkit-tap-highlight-color: transparent !important;">
    <!-- 回复标识（如果是回复） -->
    <text wx:if="{{processedComment.reply_type === 'reply'}}" class="reply-prefix">
      回复 {{processedComment.reply_to_username}}:
    </text>

    <!-- 文字内容 - 完全使用原本的text样式 -->
    <view style="line-height: 45rpx; overflow: hidden; word-wrap: break-word; margin-top:15rpx;"
          class="text">{{processedComment.content}}</view>
  </view>

  <!-- 图片内容 - 完全使用原本的image-container结构 -->
  <view wx:if="{{processedComment.images && processedComment.images.length > 0}}"
        class="comment-images image-container"
        catch:tap="onReplyClick">
    <image wx:for="{{processedComment.images}}"
           wx:key="index"
           wx:for-item="img"
           src="{{img}}"
           mode="aspectFill"
           class="comment-image uniform-image"
           catchtap="onImageClick"
           data-url="{{img}}"
           data-index="{{index}}">
    </image>
  </view>

  <!-- 互动区域 - 完全使用原本的kuang样式 -->
  <view class="comment-actions kuang"
        style="display: flex; justify-content: space-between; align-items: center; height: 50rpx; -webkit-tap-highlight-color: transparent !important;">
    <!-- 时间显示 -->
    <view class="gradient-text"
          style="line-height: 50rpx; flex: 1; -webkit-tap-highlight-color: transparent !important;"
          bindtap="onReplyClick">{{processedComment.send_timestamp}}</view>

    <!-- 点赞按钮 - 完全使用原本的last样式 -->
    <view class="action-btn last"
          style="font-size: 25rpx; display: flex; align-items: center; -webkit-tap-highlight-color: transparent !important;"
          catchtap="onLikeClick">
      <image src="/images/{{processedComment.is_liked ? 'icon-2' : 'icon'}}.png"
             mode="aspectFit"
             style="width: 35rpx; height: 35rpx; -webkit-tap-highlight-color: transparent !important;" />
      <view style="margin-left: 10rpx; -webkit-tap-highlight-color: transparent !important;">{{processedComment.total_likes || 0}}</view>
    </view>
  </view>

  <!-- 显示评论的回复 - 集成到组件内部 -->
  <view wx:if="{{processedComment.replies && processedComment.replies.length > 0}}"
        class="replies-container"
        style="padding-left: 20rpx; margin-top: 10rpx;">
    <view wx:for="{{processedComment.replies}}"
          wx:key="reply_index"
          wx:for-item="reply"
          wx:for-index="reply_index"
          wx:if="{{reply_index < (expandedReplies || defaultRepliesShow)}}"
          class="reply-item {{reply.id === highlightedReplyId ? 'highlight-item' : ''}} {{reply.id === longPressReplyId ? 'active-longpress' : ''}}"
          style="border-left: 1px solid {{processedComment.isAnonymousDisplay ? '#666' : '#ddd'}}; padding-left: 10rpx; -webkit-tap-highlight-color: transparent !important; {{processedComment.isAnonymousDisplay ? 'background-color: #4a4a4a; border-radius: 8rpx; margin-bottom: 8rpx; padding: 10rpx;' : ''}}"
          id="reply-{{reply.id}}"
          bindlongpress="onReplyLongPress"
          data-reply="{{reply}}">

      <view class="reply-content"
            bindtap="onReplyToReplyClick"
            data-reply="{{reply}}"
            style="-webkit-tap-highlight-color: transparent !important;">
        <view class="touxiang1" style="display: flex; align-items: center;">
          <image src="{{reply.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
          <view style="flex: 1; display: flex; align-items: center;">
            <text style="margin-left: 15rpx; {{processedComment.isAnonymousDisplay ? 'color: #ffffff;' : ''}}">{{reply.anonymous_name || reply.username}}</text>
            <text wx:if="{{reply.titlename && reply.titlename !== '无' && reply.titlename !== 'undefined'}}" class="title-badge bg{{reply.titlecolor}}">{{reply.titlename}}</text>
          </view>
        </view>
        <!-- 回复内容部分 -->
        <view style="line-height: 45rpx; overflow: hidden; word-wrap: break-word; margin-top:15rpx; {{processedComment.isAnonymousDisplay ? 'color: #ffffff;' : ''}}" class="text">
          <text wx:if="{{reply.reply_type === 'reply'}}" style="color: {{processedComment.isAnonymousDisplay ? '#cccccc' : '#808080'}};">回复 {{reply.reply_to_username}}: </text>
          <text>{{reply.content}}</text>
        </view>
        <!-- 添加回复图片显示 -->
        <view wx:if="{{reply.images && reply.images.length > 0}}" class="image-container" catch:tap="onReplyToReplyClick" data-reply="{{reply}}">
          <image wx:for="{{reply.images}}" wx:key="index" wx:for-item="img"
                 src="{{img}}" mode="aspectFill" class="uniform-image"
                 catchtap="onReplyImageClick"
                 data-url="{{img}}"
                 data-images="{{reply.images}}">
          </image>
        </view>
      </view>
      <!-- 回复内容下方的时间和点赞区域 -->
      <view class="kuang"
            style="display: flex; justify-content: space-between; align-items: center; height: 50rpx; padding-left: 10rpx; -webkit-tap-highlight-color: transparent !important;">
        <view class="gradient-text" style="line-height: 50rpx; flex: 1; -webkit-tap-highlight-color: transparent !important;" bindtap="onReplyToReplyClick" data-reply="{{reply}}">{{reply.send_timestamp}}</view>
        <view class="last" style="font-size: 25rpx; display: flex; align-items:center;gap: 4.5rpx; -webkit-tap-highlight-color: transparent !important; {{processedComment.isAnonymousDisplay ? 'color: #cccccc;' : ''}}" catchtap="onReplyLike" data-reply="{{reply}}">
          <image src="/images/{{reply.is_liked ? 'icon-2' : 'icon'}}.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; -webkit-tap-highlight-color: transparent !important;" />
          <text style="min-width: 20rpx; text-align: right; -webkit-tap-highlight-color: transparent !important; {{processedComment.isAnonymousDisplay ? 'color: #cccccc;' : ''}}">{{reply.total_likes || 0}}</text>
        </view>
      </view>
    </view>

    <!-- 展开/收起按钮 -->
    <view wx:if="{{processedComment.replies.length > defaultRepliesShow}}"
          class="expand-replies-btn"
          bindtap="toggleReplies"
          style="{{processedComment.isAnonymousDisplay ? 'color: #cccccc;' : ''}}">
      <view wx:if="{{!expandedReplies || expandedReplies < processedComment.replies.length}}" class="expand-text">
        <text class="expand-icon">↓</text> 展开 {{expandedReplies ? (processedComment.replies.length - expandedReplies) : (processedComment.replies.length - defaultRepliesShow)}} 条回复
      </view>
      <view wx:else class="expand-text">
        <text class="expand-icon">↑</text> 收起回复
      </view>
    </view>
  </view>
</view>
