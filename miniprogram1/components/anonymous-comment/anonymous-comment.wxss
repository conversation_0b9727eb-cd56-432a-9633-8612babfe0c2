/* components/anonymous-comment/anonymous-comment.wxss */

.anonymous-comment-container {
  /* 完全使用原本的num-item2样式 */
  margin: 16rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  border-radius: 34rpx;
  line-height: 60rpx;
  padding: 25rpx 25rpx 10rpx 25rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  word-wrap: break-word;
  transition: all 0.3s ease;
  position: relative;
  isolation: isolate;
}

/* 匿名样式 */
.anonymous-style {
  /* 匿名评论专用背景 - 深灰色，比帖子稍浅 */
  background-color: #3a3a3a;
}

.anonymous-style:active {
  background-color: #2a2a2a;
}

/* 普通样式 */
.normal-style {
  background-color: #ffffff;
}

.normal-style:active {
  background-color: rgba(0, 0, 0, 0.05);
}

/* 用户信息区域 - 完全使用原本的touxiang1样式 */
.comment-user-info {
  /* 完全使用原本的touxiang1样式 */
  margin-bottom: 10rpx;
  height: 60rpx;
  display: flex;
  background-color: transparent;
  border: 3rpx;
  align-items: center;
}

.avatar-container {
  margin-right: 15rpx;
}

/* 匿名头像 */
.anonymous-avatar {
  /* 完全使用原本的头像样式 */
  height: 70rpx;
  width: 70rpx;
  border-radius: 15%;
}

/* 普通头像 */
.normal-avatar {
  height: 70rpx;
  width: 70rpx;
  border-radius: 15%;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.username-container {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

/* 用户名样式 */
.anonymous-username {
  color: #ffffff; /* 匿名用户名使用白色 */
  font-size: 30rpx;
  margin-right: 15rpx;
}

.normal-username {
  color: #333;
  font-size: 30rpx;
  margin-right: 15rpx;
}

/* 头衔样式 - 完全使用原本的touxian2样式 */
.title-badge {
  /* 完全使用原本的touxian2样式 */
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
  color: white;
}

.anonymous-title {
  background: #666;
}

.normal-title {
  background: #666;
}

/* 原有的头衔颜色类 */
.bg0 { background: #666; }
.bg1 { background: #ff6b6b; }
.bg2 { background: #4ecdc4; }
.bg3 { background: #45b7d1; }
.bg4 { background: #96ceb4; }
.bg5 { background: #ffeaa7; }

.comment-time {
  color: #999;
  font-size: 24rpx;
}

/* 匿名评论中的时间显示 */
.anonymous-style .comment-time {
  color: #cccccc;
}

/* 评论内容 - 完全使用原本的text样式 */
.comment-content {
  /* 完全使用原本的text样式 */
  line-height: 45rpx;
  overflow: hidden;
  word-wrap: break-word;
  margin-top: 15rpx;
  font-size: 30rpx;
  margin-bottom: 15rpx;
  -webkit-tap-highlight-color: transparent !important;
}

/* 匿名评论内容文字颜色 */
.anonymous-style .comment-content {
  color: #ffffff;
}

/* 普通评论内容文字颜色 */
.normal-style .comment-content {
  color: #333;
}

.reply-prefix {
  color: #cccccc;
  font-size: 26rpx;
}

/* 评论图片 - 完全使用原本的image-container样式 */
.comment-images {
  /* 完全使用原本的image-container样式 */
  margin-top: 15rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 9rpx;
  margin-bottom: 16rpx;
  z-index: 5;
  position: relative;
  -webkit-tap-highlight-color: transparent !important;
}

.comment-image {
  /* 完全使用原本的uniform-image样式 */
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
  z-index: 10;
  position: relative;
}

/* 互动区域 - 完全使用原本的kuang样式 */
.comment-actions {
  /* 完全使用原本的kuang样式 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50rpx;
  -webkit-tap-highlight-color: transparent !important;
}

.action-btn {
  /* 完全使用原本的last样式 */
  position: relative;
  z-index: 3;
  padding: 0 10rpx;
  font-size: 25rpx;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  gap: 10rpx;
  -webkit-tap-highlight-color: transparent !important;
}

.action-btn:active {
  background-color: transparent !important;
}

.action-icon {
  /* 完全使用原本的action-icon样式 */
  width: 35rpx;
  height: 35rpx;
  -webkit-tap-highlight-color: transparent !important;
}

.action-text {
  font-size: 25rpx;
  display: inline-block;
  min-width: 20rpx;
  text-align: left;
  line-height: 35rpx;
  -webkit-tap-highlight-color: transparent !important;
}

/* 匿名评论中的文字颜色 */
.anonymous-style .action-text {
  color: #cccccc;
}

/* 普通评论中的文字颜色 */
.normal-style .action-text {
  color: #666;
}

/* 时间显示样式 - 完全使用原本的gradient-text */
.gradient-text {
  background: linear-gradient(to right, #e434a9, #cc4444);
  -webkit-background-clip: text;
  color: transparent;
  font-size: 28rpx;
  line-height: 35rpx;
  -webkit-tap-highlight-color: transparent !important;
  flex: 1;
}

/* 原本的样式类 */
.touxiang1 {
  margin-bottom: 10rpx;
  height: 60rpx;
  display: flex;
  background-color: transparent;
  border: 3rpx;
  align-items: center;
}

.kuang {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50rpx;
  -webkit-tap-highlight-color: transparent !important;
}

.last {
  position: relative;
  z-index: 3;
  padding: 0 10rpx;
  font-size: 25rpx;
  display: flex;
  align-items: center;
  -webkit-tap-highlight-color: transparent !important;
}

.text {
  line-height: 45rpx;
  overflow: hidden;
  word-wrap: break-word;
  margin-top: 15rpx;
  font-size: 30rpx;
  margin-bottom: 15rpx;
}

.image-container {
  margin-top: 15rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 9rpx;
  margin-bottom: 16rpx;
  z-index: 5;
  position: relative;
  -webkit-tap-highlight-color: transparent !important;
}

.uniform-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
  z-index: 10;
  position: relative;
}

/* 回复相关样式 */
.replies-container {
  padding-left: 20rpx;
  margin-top: 10rpx;
}

.reply-item {
  margin-bottom: 8rpx;
  border-left: 1px solid #666;
  padding-left: 10rpx;
  -webkit-tap-highlight-color: transparent !important;
}

.reply-content {
  -webkit-tap-highlight-color: transparent !important;
}

/* 展开回复按钮样式 */
.expand-replies-btn {
  position: relative;
  z-index: 3;
  background-color: transparent;
  padding: 5rpx 0;
  font-size: 28rpx;
  margin-top: 5rpx;
  text-align: center;
}

.expand-text {
  display: inline-block;
  padding: 5rpx 20rpx;
  font-size: 28rpx;
}

.expand-icon {
  margin-right: 8rpx;
  font-weight: bold;
}

/* 匿名回复样式 */
.anonymous-style .reply-item {
  background-color: #4a4a4a;
  border-radius: 8rpx;
  padding: 10rpx;
  border-left: 1px solid #666;
}

.anonymous-style .reply-content .text {
  color: #ffffff;
}

.anonymous-style .reply-content .touxiang1 text {
  color: #ffffff;
}

.anonymous-style .expand-replies-btn {
  color: #cccccc;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .anonymous-comment-container {
    padding: 20rpx;
  }
  
  .anonymous-avatar,
  .normal-avatar {
    width: 50rpx;
    height: 50rpx;
  }
  
  .avatar-text {
    font-size: 20rpx;
  }
  
  .comment-image {
    width: 100rpx;
    height: 100rpx;
  }
}
