// components/anonymous-post/anonymous-post.js
Component({
  properties: {
    // 帖子数据
    postData: {
      type: Object,
      value: {},
      observer: function(newVal) {
        if (newVal && newVal.is_anonymous_display) {
          this.processAnonymousData(newVal);
        }
      }
    },
    // 是否显示详细信息（用于区分列表和详情页）
    showDetail: {
      type: Boolean,
      value: false
    },
    // 当前用户信息
    currentUser: {
      type: Object,
      value: {}
    }
  },

  data: {
    processedPost: {},
    isOriginalPoster: false
  },

  methods: {
    // 处理匿名数据
    processAnonymousData(postData) {
      const processedPost = { ...postData };
      
      // 检查是否是原帖作者
      const isOriginalPoster = this.checkIsOriginalPoster(postData);
      
      // 设置头衔
      if (processedPost.is_anonymous_display) {
        processedPost.displayTitle = isOriginalPoster ? '帖主' : '';
        // 头像URL由后端提供，不需要前端生成
      }

      this.setData({
        processedPost: processedPost,
        isOriginalPoster: isOriginalPoster
      });
    },

    // 检查是否是原帖作者
    checkIsOriginalPoster(postData) {
      // 这里需要根据你的业务逻辑来判断
      // 可能需要传入原帖的作者信息进行比较
      return postData.is_original_poster || false;
    },

    // 注：头像URL由后端提供，前端不再生成头像

    // 处理帖子点击
    onPostClick(e) {
      if (!this.data.showDetail) {
        // 列表页面，跳转到详情
        this.triggerEvent('postclick', {
          postData: this.data.processedPost
        });
      }
    },

    // 处理点赞
    onLikeClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('like', {
        postData: this.data.processedPost
      });
    },

    // 处理评论点击
    onCommentClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('comment', {
        postData: this.data.processedPost
      });
    },

    // 处理图片点击
    onImageClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      const { url, index } = e.currentTarget.dataset;
      this.triggerEvent('imageclick', {
        url: url,
        index: index,
        images: this.data.processedPost.images
      });
    },

    // 处理长按
    onLongPress(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('longpress', {
        postData: this.data.processedPost
      });
    },

    // 处理联系按钮点击
    onContactClick(e) {
      if (e && e.stopPropagation) {
        e.stopPropagation();
      }
      this.triggerEvent('contact', {
        postData: this.data.processedPost
      });
    }
  }
});
