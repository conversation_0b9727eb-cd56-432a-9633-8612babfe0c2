<!-- components/anonymous-post/anonymous-post.wxml -->
<view class="anonymous-post-container"
      bindtap="onPostClick"
      bindlongpress="onLongPress">

  <!-- 匿名标识 -->
  <view class="anonymous-badge">
    <text class="anonymous-text">匿名</text>
  </view>

  <!-- 用户信息区域 - 完全使用原本的touxiang1结构 -->
  <view class="user-info-section touxiang1" style="display: flex; align-items: center;">
    <image src="{{processedPost.face_url}}" class="anonymous-avatar"></image>
    <view wx:if="{{!processedPost.weizhi}}" style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
      <view style="display: flex; align-items: center;">
        <text class="anonymous-username" style="margin-left:15rpx;">{{processedPost.username}}</text>
        <text wx:if="{{processedPost.displayTitle}}" class="title-badge">{{processedPost.displayTitle}}</text>
      </view>
      <!-- 时间显示移到用户名下方 -->
      <view style="margin-left:15rpx; margin-top: 5rpx;">
        <text class="post-time">{{processedPost.time_display}}</text>
      </view>
    </view>

    <!-- 如果有位置信息 -->
    <view wx:if="{{processedPost.weizhi}}" style="flex: 1; display: flex; flex-direction: column; justify-content: center;">
      <view style="display: flex; align-items: center;">
        <text class="anonymous-username" style="margin-left:15rpx;">{{processedPost.username}}</text>
        <text wx:if="{{processedPost.displayTitle}}" class="title-badge">{{processedPost.displayTitle}}</text>
      </view>
      <view style="margin-left:15rpx; margin-top: 5rpx;">
        <text style="font-size: 24rpx;color: #cccccc;">{{processedPost.weizhi}}</text>
      </view>
      <!-- 时间显示 -->
      <view style="margin-left:15rpx; margin-top: 5rpx;">
        <text class="post-time">{{processedPost.time_display}}</text>
      </view>
    </view>
  </view>

  <!-- 帖子内容 - 完全使用原本的结构 -->
  <rich-text user-select="{{showDetail}}"
             style="letter-spacing: 0rpx; line-height: 45rpx; overflow: hidden; margin-top: 10rpx;"
             class="content-text">{{processedPost.content}}</rich-text>

  <!-- 图片内容 - 完全使用原本的image-container结构 -->
  <view wx:if="{{processedPost.images && processedPost.images.length > 0}}">
    <view class="images-container">
      <image wx:for="{{processedPost.images}}"
             wx:key="index"
             src="{{item}}"
             mode="aspectFill"
             class="post-image uniform-image"
             bindtap="onImageClick"
             data-url="{{item}}"
             data-index="{{index}}">
      </image>
    </view>
  </view>

  <!-- 底部信息区域 - 完全使用原本的kuang样式 -->
  <view class="interaction-section kuang" style="display: flex; justify-content: space-between; align-items: center;">
    <!-- 时间显示 - 完全使用原本的gradient-text -->
    <view class="gradient-text" style="line-height: 35rpx;">{{processedPost.send_timestamp}}</view>

    <!-- 右侧信息 - 完全使用原本的last样式 -->
    <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
      <!-- 有联系方式时显示联系按钮和金额 -->
      <block wx:if="{{processedPost.vx || processedPost.qq || processedPost.phonenumber}}">
        <view wx:if="{{processedPost.jine}}" style="color: rgb(226, 114, 23); margin-right: 15rpx;">{{processedPost.jine}}</view>
        <view class="contact-btn" bindtap="onContactClick">
          <text class="contact-text">联系</text>
          <image src="/images/fenxiang2.png" class="action-icon" />
        </view>
      </block>
      <!-- 无联系方式时，只显示金额 -->
      <view wx:else style="color: rgb(226, 114, 23);">{{processedPost.jine}}</view>
    </view>
  </view>
</view>
