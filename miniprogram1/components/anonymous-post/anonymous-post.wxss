/* components/anonymous-post/anonymous-post.wxss */

.anonymous-post-container {
  /* 完全使用原本的num-item2样式 */
  margin: 16rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  border-radius: 34rpx;
  line-height: 60rpx;
  padding: 25rpx 25rpx 10rpx 25rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  word-wrap: break-word;
  /* 匿名帖子专用背景 - 深灰色 */
  background-color: #2c2c2c;
  transition: all 0.3s ease;
  position: relative;
  isolation: isolate;
}

.anonymous-post-container:active {
  background-color: #1a1a1a;
}

/* 匿名标识 */
.anonymous-badge {
  position: absolute;
  top: 15rpx;
  right: 15rpx;
  display: flex;
  align-items: center;
  background: #666;
  color: white;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
  font-size: 22rpx;
  font-weight: bold;
  z-index: 10;
}

.anonymous-icon {
  width: 20rpx;
  height: 20rpx;
  margin-right: 4rpx;
}

.anonymous-text {
  font-size: 22rpx;
}

/* 用户信息区域 - 完全使用原本的touxiang1样式 */
.user-info-section {
  /* 完全使用原本的touxiang1样式 */
  margin-bottom: 10rpx;
  height: 60rpx;
  display: flex;
  background-color: transparent;
  border: 3rpx;
  align-items: center;
  padding-right: 100rpx; /* 为匿名标识留出空间 */
}

.user-avatar-container {
  margin-right: 15rpx;
}

.anonymous-avatar {
  /* 完全使用原本的头像样式 */
  height: 70rpx;
  width: 70rpx;
  border-radius: 15%;
}

.user-details {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.username-container {
  display: flex;
  align-items: center;
  margin-bottom: 6rpx;
}

.anonymous-username {
  /* 匿名用户名使用白色文字 */
  color: #ffffff;
  font-size: 30rpx;
  margin-right: 15rpx;
}

.title-badge {
  /* 完全使用原本的touxian2样式 */
  background: #666;
  color: white;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
  font-size: 20rpx;
  font-weight: bold;
}

.post-time {
  color: #999;
  font-size: 24rpx;
}

/* 匿名帖子中的时间显示 */
.anonymous-post-container .post-time {
  color: #cccccc;
}

/* 帖子内容区域 */
.post-content-section {
  margin-bottom: 20rpx;
}

.content-text {
  /* 完全使用原本的text样式 */
  letter-spacing: 0rpx;
  line-height: 45rpx;
  overflow: hidden;
  margin-top: 10rpx;
  font-size: 30rpx;
  color: #ffffff; /* 匿名帖子文字使用白色 */
  word-wrap: break-word;
}

/* 图片容器 - 完全使用原本的image-container样式 */
.images-container {
  margin-top: 15rpx;
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 9rpx;
  margin-bottom: 16rpx;
  z-index: 5;
  position: relative;
  -webkit-tap-highlight-color: transparent !important;
}

.post-image {
  /* 完全使用原本的uniform-image样式 */
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
  z-index: 10;
  position: relative;
}

/* 额外信息 */
.extra-info {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-bottom: 15rpx;
}

.location-info,
.price-info {
  background: rgba(255, 255, 255, 0.1);
  color: #ffffff;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

/* 互动区域 - 完全使用原本的kuang样式 */
.interaction-section {
  /* 完全使用原本的kuang样式 */
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50rpx;
  margin-top: 10rpx;
}

.action-btn {
  /* 完全使用原本的last样式 */
  position: relative;
  z-index: 3;
  padding: 0 10rpx;
  font-size: 25rpx;
  display: flex;
  align-items: center;
  background-color: transparent;
  border: none;
  gap: 10rpx;
  -webkit-tap-highlight-color: transparent !important;
}

.action-btn:active {
  background-color: transparent !important;
}

.action-icon {
  /* 完全使用原本的action-icon样式 */
  width: 35rpx;
  height: 35rpx;
}

.action-text {
  font-size: 25rpx;
  color: #cccccc; /* 匿名帖子中的文字使用浅灰色 */
  display: inline-block;
  min-width: 20rpx;
  text-align: left;
  line-height: 35rpx;
}

.view-count {
  margin-left: auto;
}

.view-text {
  color: #999;
  font-size: 24rpx;
}

/* 时间显示样式 - 完全使用原本的gradient-text */
.gradient-text {
  background: linear-gradient(to right, #e434a9, #cc4444);
  -webkit-background-clip: text;
  color: transparent;
  font-size: 28rpx;
  line-height: 35rpx;
}

/* 联系按钮样式 */
.contact-btn {
  display: flex;
  align-items: center;
  background-color: #07c160;
  color: white;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
}

.contact-text {
  margin-right: 6rpx;
}

/* 最后一行样式 */
.last {
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
}

/* 原本的样式类 */
.touxiang1 {
  margin-bottom: 10rpx;
  height: 60rpx;
  display: flex;
  background-color: transparent;
  border: 3rpx;
  align-items: center;
}

.kuang {
  display: flex;
  justify-content: space-between;
  align-items: center;
  height: 50rpx;
  margin-top: 10rpx;
}

.uniform-image {
  width: 100%;
  height: 200rpx;
  border-radius: 8rpx;
  object-fit: cover;
  z-index: 10;
  position: relative;
}

/* 响应式调整 */
@media (max-width: 750rpx) {
  .anonymous-post-container {
    margin: 16rpx;
    padding: 24rpx;
  }
  
  .user-info-section {
    padding-right: 100rpx;
  }
  
  .anonymous-badge {
    padding: 6rpx 12rpx;
    font-size: 22rpx;
  }
  
  .post-image {
    width: 160rpx;
    height: 160rpx;
  }
}
