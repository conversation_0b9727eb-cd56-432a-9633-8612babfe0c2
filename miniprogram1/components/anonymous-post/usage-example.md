# 匿名帖子组件使用说明

## 1. 在页面中引入组件

### 在 messageDetail.json 中添加组件引用：
```json
{
  "usingComponents": {
    "anonymous-post": "/components/anonymous-post/anonymous-post",
    "anonymous-comment": "/components/anonymous-comment/anonymous-comment"
  }
}
```

## 2. 在 WXML 中使用组件

### 帖子显示：
```xml
<!-- 原有的帖子显示逻辑 -->
<view wx:if="{{!message.is_anonymous_display}}" class="original-post-container">
  <!-- 你原有的帖子显示代码 -->
</view>

<!-- 匿名帖子显示 -->
<anonymous-post wx:if="{{message.is_anonymous_display}}"
                post-data="{{message}}"
                show-detail="{{true}}"
                current-user="{{userInfo}}"
                bind:like="onPostLike"
                bind:comment="onPostComment"
                bind:imageclick="onPostImageClick"
                bind:longpress="onPostLongPress">
</anonymous-post>
```

### 评论显示：
```xml
<!-- 评论列表 -->
<view wx:for="{{comment}}" wx:key="id" wx:for-item="item">
  <!-- 原有评论显示逻辑 -->
  <view wx:if="{{!message.is_anonymous_display}}" class="original-comment">
    <!-- 你原有的评论显示代码 -->
  </view>
  
  <!-- 匿名帖子的评论显示 -->
  <anonymous-comment wx:if="{{message.is_anonymous_display}}"
                     comment-data="{{item}}"
                     is-from-anonymous-post="{{true}}"
                     original-poster-id="{{message.user_id}}"
                     current-user="{{userInfo}}"
                     bind:reply="onCommentReply"
                     bind:like="onCommentLike"
                     bind:imageclick="onCommentImageClick"
                     bind:longpress="onCommentLongPress">
  </anonymous-comment>
</view>
```

## 3. 在 JS 中处理事件

### 在 messageDetail.js 中添加事件处理：
```javascript
// 帖子相关事件
onPostLike(e) {
  const postData = e.detail.postData;
  // 处理帖子点赞逻辑
},

onPostComment(e) {
  const postData = e.detail.postData;
  // 处理帖子评论逻辑
  this.handleCommentClick();
},

onPostImageClick(e) {
  const { url, index, images } = e.detail;
  // 处理图片预览
  wx.previewImage({
    current: url,
    urls: images
  });
},

onPostLongPress(e) {
  const postData = e.detail.postData;
  // 处理长按操作（举报等）
},

// 评论相关事件
onCommentReply(e) {
  const commentData = e.detail.commentData;
  // 处理评论回复
  this.onReplyClick({
    currentTarget: {
      dataset: {
        id: commentData.id,
        username: commentData.anonymous_name || commentData.username,
        type: 'comment',
        userid: commentData.user_id
      }
    }
  });
},

onCommentLike(e) {
  const commentData = e.detail.commentData;
  // 处理评论点赞
},

onCommentImageClick(e) {
  const { url, index, images } = e.detail;
  // 处理评论图片预览
  wx.previewImage({
    current: url,
    urls: images
  });
},

onCommentLongPress(e) {
  const commentData = e.detail.commentData;
  // 处理评论长按操作
}
```

## 4. 数据格式要求

### 帖子数据格式：
```javascript
{
  id: 123,
  content: "帖子内容",
  username: "匿·温柔的清风", // 匿名名称
  face_url: "https://www.bjgaoxiaoshequ.store/images/weixiao.png", // 随机头像URL
  is_anonymous_display: true, // 标识是否匿名显示
  is_original_poster: true, // 是否是原帖作者
  images: ["url1", "url2"],
  total_likes: 10,
  comment_count: 5,
  views: 100,
  time_display: "2小时前"
}
```

### 评论数据格式：
```javascript
{
  id: 456,
  content: "评论内容",
  username: "匿·安静的暖阳", // 匿名名称
  anonymous_name: "匿·安静的暖阳", // 匿名名称
  face_url: "https://www.bjgaoxiaoshequ.store/images/weixiao.png", // 随机头像URL
  is_original_poster: false, // 是否是原帖作者
  reply_type: "comment", // 或 "reply"
  reply_to_username: "匿·温柔的清风",
  images: ["url1"],
  total_likes: 3,
  time_display: "1小时前"
}
```

## 5. 样式自定义

如果需要调整样式，可以在页面的 WXSS 中覆盖组件样式：

```css
/* 调整匿名帖子的边框颜色 */
.anonymous-post-container {
  border-left-color: #your-color !important;
}

/* 调整匿名用户名颜色 */
.anonymous-username {
  color: #your-color !important;
}
```

## 6. 注意事项

1. 确保后端返回的数据已经处理过敏感信息
2. 匿名名称应该在后端生成，前端只负责显示
3. 组件会自动处理匿名和非匿名的样式切换
4. 头衔显示逻辑：帖主显示"帖主"，其他用户不显示头衔
5. 所有的用户交互事件都会通过自定义事件传递给父页面处理
