// components/character-counter/character-counter.js
Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 当前字符数
    count: {
      type: Number,
      value: 0
    },
    // 最大字符数
    max: {
      type: Number,
      value: 100
    },
    // 文本颜色
    color: {
      type: String,
      value: '#888'
    },
    // 警告颜色（达到90%及以上时的颜色）
    warningColor: {
      type: String,
      value: '#ff9500'
    },
    // 错误颜色（超过限制时的颜色）
    errorColor: {
      type: String,
      value: '#ff3b30'
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    currentColor: '#888'
  },

  /**
   * 数据监听器
   */
  observers: {
    'count, max': function(count, max) {
      let currentColor = this.data.color;
      const ratio = count / max;
      
      if (count > max) {
        currentColor = this.data.errorColor;
      } else if (ratio >= 0.9) {
        currentColor = this.data.warningColor;
      }
      
      this.setData({
        currentColor: currentColor
      });
    }
  }
}) 