<view wx:if="{{show}}" class="comment-input-container">
  <!-- 遮罩层 -->
  <view class="mask" bindtap="close"></view>
  
  <!-- 评论输入区域 -->
  <view class="comment-input-section">
    <!-- 图片预览区域 -->
    <view wx:if="{{selectedImages.length > 0}}" class="image-preview-section">
      <view class="image-preview-list">
        <view class="image-preview-item" wx:for="{{selectedImages}}" wx:key="index">
          <image src="{{item}}" mode="aspectFill" class="preview-image" />
          <view class="remove-image" bindtap="removeImage" data-index="{{index}}">×</view>
        </view>
      </view>
    </view>

    <!-- 输入框和工具栏 -->
    <view class="input-tools-container">
      <!-- 评论输入框 -->
      <textarea class="comment-textarea"
                placeholder="{{placeholder}}"
                value="{{content}}"
                bindinput="onInput"
                bindblur="handleInputBlur"
                bindfocus="handleInputFocus"
                focus="{{focused}}"
                maxlength="200"
                auto-height="{{true}}"
                style="min-height: 80rpx; max-height: 200rpx;" />

      <!-- 工具栏 -->
      <view class="tools-bar">
        <!-- 图片按钮 -->
        <view class="tool-item" bindtap="chooseImage">
          <image src="/images/tupian.png" class="tool-icon" />
        </view>
        
        <!-- 相机按钮 -->
        <view class="tool-item" bindtap="takePhoto">
          <image src="/images/xiangji.png" class="tool-icon" />
        </view>

        <!-- 发送按钮 -->
        <view class="send-btn {{canSend ? 'active' : ''}}" bindtap="submit">
          发送
        </view>
      </view>
    </view>
  </view>
</view>
