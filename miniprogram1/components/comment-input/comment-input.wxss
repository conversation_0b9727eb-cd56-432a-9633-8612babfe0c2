/* components/comment-input/comment-input.wxss */
.comment-input-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
}

.mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.3);
}

.comment-input-section {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  background: white;
  border-radius: 20rpx 20rpx 0 0;
  padding: 20rpx;
  max-height: 60vh;
  display: flex;
  flex-direction: column;
}

/* 图片预览区域 */
.image-preview-section {
  margin-bottom: 20rpx;
  max-height: 200rpx;
  overflow-y: auto;
}

.image-preview-list {
  display: flex;
  flex-wrap: wrap;
  gap: 10rpx;
}

.image-preview-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 10rpx;
}

.remove-image {
  position: absolute;
  top: -10rpx;
  right: -10rpx;
  width: 40rpx;
  height: 40rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
}

/* 输入框和工具栏 */
.input-tools-container {
  display: flex;
  flex-direction: column;
  gap: 15rpx;
}

.comment-textarea {
  width: 100%;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 15rpx;
  font-size: 28rpx;
  line-height: 1.5;
  background: #f8f8f8;
  box-sizing: border-box;
}

.comment-textarea:focus {
  border-color: #007aff;
  background: white;
}

.tools-bar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0 10rpx;
}

.tool-item {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 10rpx;
  background: #f0f0f0;
  margin-right: 15rpx;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
}

.send-btn {
  padding: 15rpx 30rpx;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #999;
  background: #f0f0f0;
  transition: all 0.3s;
}

.send-btn.active {
  background: #007aff;
  color: white;
}
