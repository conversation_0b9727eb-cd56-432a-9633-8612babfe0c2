Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 是否显示弹出层
    visible: {
      type: Boolean,
      value: false
    },
    // 弹出层位置: bottom(底部弹出), center(中间弹出)
    position: {
      type: String,
      value: 'bottom'
    },
    // 弹出层标题
    title: {
      type: String,
      value: ''
    },
    // 是否显示关闭按钮
    showClose: {
      type: Boolean,
      value: true
    },
    // 是否使用模糊背景
    useBlur: {
      type: Boolean,
      value: true
    },
    // 遮罩层颜色
    maskColor: {
      type: String,
      value: 'rgba(0, 0, 0, 0.5)'
    },
    // 弹出层宽度，仅position为center时有效
    width: {
      type: String,
      value: '80%'
    },
    // 自定义弹出层样式
    customStyle: {
      type: String,
      value: ''
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 关闭弹出层
    onClose: function() {
      this.triggerEvent('close');
    },
    
    // 阻止遮罩层点击事件冒泡
    stopPropagation: function(e) {
      e.stopPropagation();
    },
    
    // 阻止遮罩层滑动事件
    preventTouchMove: function() {
      return false;
    }
  }
}) 