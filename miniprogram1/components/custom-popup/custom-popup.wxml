<view class="popup-container {{visible ? 'popup-show' : ''}}" 
      style="background-color: {{maskColor}}; {{useBlur ? 'backdrop-filter: blur(3px);' : ''}}"
      catchtap="onClose" catchtouchmove="preventTouchMove">
  
  <!-- 底部弹出样式 -->
  <view wx:if="{{position === 'bottom'}}" 
        class="popup-content popup-bottom {{visible ? 'popup-bottom-show' : ''}}" 
        style="{{customStyle}}"
        catchtap="stopPropagation">
    <view class="popup-header" wx:if="{{title || showClose}}">
      <view class="popup-title" wx:if="{{title}}">{{title}}</view>
      <view class="popup-close" catchtap="onClose" wx:if="{{showClose}}">
        <image src="/images/guanbi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="popup-body">
      <slot></slot>
    </view>
  </view>
  
  <!-- 中间弹出样式 -->
  <view wx:if="{{position === 'center'}}" 
        class="popup-content popup-center {{visible ? 'popup-center-show' : ''}}" 
        style="width: {{width}}; {{customStyle}}"
        catchtap="stopPropagation">
    <view class="popup-header" wx:if="{{title || showClose}}">
      <view class="popup-title" wx:if="{{title}}">{{title}}</view>
      <view class="popup-close" catchtap="onClose" wx:if="{{showClose}}">
        <image src="/images/guanbi.png" mode="aspectFit"></image>
      </view>
    </view>
    <view class="popup-body">
      <slot></slot>
    </view>
  </view>
</view> 