/* 弹出层容器 */
.popup-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.popup-show {
  visibility: visible;
  opacity: 1;
}

/* 弹出内容公共样式 */
.popup-content {
  background-color: #ffffff;
  position: relative;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

/* 底部弹出样式 */
.popup-bottom {
  width: 100%;
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  padding: 30rpx 30rpx 40rpx;
  position: absolute;
  bottom: 0;
  transform: translateY(100%);
  transition: transform 0.3s ease;
}

.popup-bottom-show {
  transform: translateY(0);
}

/* 中间弹出样式 */
.popup-center {
  border-radius: 20rpx;
  padding: 30rpx;
  max-width: 90%;
  transform: scale(0.8);
  opacity: 0;
  transition: all 0.3s ease;
}

.popup-center-show {
  transform: scale(1);
  opacity: 1;
}

/* 头部样式 */
.popup-header {
  display: flex;
  justify-content: center;
  align-items: center;
  position: relative;
  margin-bottom: 30rpx;
}

.popup-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  line-height: 1.5;
  text-align: center;
}

.popup-close {
  position: absolute;
  right: 0;
  top: 50%;
  transform: translateY(-50%);
  width: 50rpx;
  height: 50rpx;
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  background-color: #f6f6f6;
  border-radius: 50%;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.08);
  transition: all 0.2s ease;
}

.popup-close:active {
  transform: translateY(-50%) scale(0.95);
  background-color: #eeeeee;
}

.popup-close image {
  width: 30rpx;
  height: 30rpx;
}

/* 内容区域样式 */
.popup-body {
  width: 100%;
} 