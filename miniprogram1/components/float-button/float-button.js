Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 按钮图标路径
    iconSrc: {
      type: String,
      value: ''
    },
    // 按钮位置样式类名
    className: {
      type: String,
      value: ''
    },
    // 是否激活状态
    active: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
  },

  /**
   * 组件的方法列表
   */
  methods: {
    handleTap() {
      // 触发点击事件
      this.triggerEvent('tap');
    }
  }
}) 