.float-button {
  position: fixed;
  color: white;
  background-color: rgba(255, 255, 255, 0.8);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 10000;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.2);
}

.button-icon {
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: block;
}

.float-button.active {
  transform: scale(1.2);
  background-color: rgba(255, 107, 107, 0.8);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
} 