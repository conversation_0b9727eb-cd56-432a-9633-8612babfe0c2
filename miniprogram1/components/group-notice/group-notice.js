Component({
  properties: {
    // 可以添加需要的属性
  },

  data: {
    show: true,
    showModal: false,
    showQRCode: false,
    qrcodeImageUrl: ''
  },

  lifetimes: {
    // 在组件实例刚刚被创建时执行
    created() {
      // 检查是否已在设置页禁用了微信群提示
      const showGroupNotice = wx.getStorageSync('showGroupNotice');
      if (showGroupNotice === false) {
        this.setData({ show: false });
      }

      // 初始化二维码图片URL
      this.updateQRCodeImage();
    },

    // 在组件实例进入页面节点树时执行
    attached() {
      // 再次检查是否应该显示群通知
      const showGroupNotice = wx.getStorageSync('showGroupNotice');
      if (showGroupNotice === false) {
        this.setData({ show: false });
        return;
      }

      // 从服务器获取最新的按钮状态
      this.checkServerSetting();

      // 更新二维码图片URL
      this.updateQRCodeImage();
    }
  },

  pageLifetimes: {
    // 页面被展示时触发
    show() {
      // 页面显示时也检查状态，确保实时响应设置变化
      const showGroupNotice = wx.getStorageSync('showGroupNotice');
      if (showGroupNotice === false) {
        this.setData({ show: false });
      }

      // 每次页面显示时更新二维码图片URL，确保获取最新图片
      this.updateQRCodeImage();
    }
  },

  methods: {
    // 更新二维码图片URL，添加时间戳防止缓存
    updateQRCodeImage() {
      const timestamp = new Date().getTime();
      const qrcodeImageUrl = `https://www.bjgaoxiaoshequ.store/tupian/微信群1.jpg?t=${timestamp}`;
      this.setData({ qrcodeImageUrl });
    },

    // 检查服务器上的设置状态
    checkServerSetting() {
      // 获取用户ID
      const userId = getApp().globalData.user_id;
      if (!userId) return;

      wx.request({
        url: getApp().globalData.wangz + '/button_setting/getButtonSetting',
        method: 'POST',
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token')
        },
        data: {
          user_id: userId,
          button_type: 'group_notice'
        },
        success: (res) => {
          if (res.data && res.data.code === 200) {
            const show = res.data.data.button_status === '1';
            this.setData({ show: show });
            // 同步到本地存储
            wx.setStorageSync('showGroupNotice', show);
          }
        }
      });
    },

    showQRCodeModal() {
      // 每次显示弹窗时更新图片URL，确保获取最新图片
      this.updateQRCodeImage();
      this.setData({ showQRCode: true });
    },
    
    closeQRCodeModal() {
      this.setData({ showQRCode: false });
    },
    
    onShowModal() {
      this.setData({ showModal: true })
    },
    
    onCloseModal() {
      this.setData({ showModal: false })
    },
    
    stopPropagation() {
      // 阻止事件冒泡
      return false
    },
    
    onConfirmClose() {
      // 请求后端接口关闭微信群提示
      wx.request({
        url: getApp().globalData.wangz + '/button_setting/saveButtonSetting',
        method: 'POST',
        header: {
          'content-type': 'application/json',
          'Authorization': wx.getStorageSync('token')
        },
        data: {
          user_id: getApp().globalData.user_id,
          button_type: 'group_notice',
          button_status: '0'
        },
        success: () => {
          wx.setStorageSync('showGroupNotice', false);
          this.setData({ show: false, showModal: false });
          wx.showToast({
            title: '已关闭微信群提示',
            icon: 'none'
          });
        },
        fail: () => {
          wx.showToast({ title: '网络错误', icon: 'none' });
        }
      })
    }
  }
}) 