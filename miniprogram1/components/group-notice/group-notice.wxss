.group-notice {
  margin: 16rpx 40rpx 40rpx 40rpx;
  box-sizing: border-box;
  border-radius: 34rpx;
  padding: 36rpx 25rpx 24rpx 25rpx;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  text-overflow: ellipsis;
  width: 90%;
  word-wrap: break-word;
  background-color: #ffffff;
  transition: all 0.3s ease;
  position: relative;
  isolation: isolate;
}

.notice-content {
  display: flex;
  align-items: flex-start;
  width: 100%;
  height: 100%;
  min-height: 90rpx;
}

.group-avatar {
  width: 90rpx;
  height: 90rpx;
  border-radius: 16rpx;
  margin-right: 18rpx;
  background-color: #f5f5f5;
  flex-shrink: 0;
}

.notice-text {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  height: 90rpx;
  min-height: 90rpx;
}

.notice-title {
  font-size: 34rpx;
  color: #333;
  font-weight: bold;
  line-height: 1;
  margin: 0;
}

.notice-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1;
  margin: 0;
}

.close-btn {
  position: absolute;
  top: 20rpx;
  right: 24rpx;
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.7;
  background: #f5f5f5;
  border-radius: 50%;
  z-index: 2;
  transition: opacity 0.2s, background 0.2s;
}

.close-btn:active {
  opacity: 1;
  background: #ececec;
}

.close-btn image {
  width: 30rpx;
  height: 30rpx;
  display: block;
}

.modal-mask {
  position: fixed;
  left: 0;
  top: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.32);
  z-index: 99999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.card-modal {
  min-width: 320rpx;
  max-width: 480rpx;
  width: 92vw;
  border-radius: 32rpx;
  background: #fff;
  padding: 64rpx 36rpx 56rpx 36rpx;
  box-shadow: 0 8rpx 32rpx rgba(0,0,0,0.10);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.modal-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-bottom: 18rpx;
  width: 100%;
}

.modal-icon-square {
  width: 90rpx;
  height: 90rpx;
  border-radius: 16rpx;
  margin-bottom: 12rpx;
  object-fit: cover;
  background: #f5f5f5;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 700;
  color: #374151;
  margin-bottom: 0;
}

.modal-message {
  margin-top: 18rpx;
  color: #6b7280;
  font-size: 28rpx;
  text-align: center;
  margin-bottom: 56rpx;
  line-height: 1.8;
}

.modal-close-btn {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  width: 50rpx;
  height: 50rpx;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #f5f5f5;
  border-radius: 50%;
  padding: 8rpx;
}

.modal-close-btn image {
  width: 32rpx;
  height: 32rpx;
  opacity: 1;
}

.modal-main-btn {
  width: 60%;
  border-radius: 999rpx;
  padding: 14rpx 0;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  border: 3rpx solid #222;
  background: #fff;
  color: #222;
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 2rpx;
  transition: background 0.2s, border-color 0.2s;
}

.modal-main-btn:active {
  background: #f2f2f2;
  border-color: #111;
}

.qrcode-modal {
  max-width: 600rpx;
  width: 90vw;
  border-radius: 24rpx;
  background: #fff;
  padding: 40rpx 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(0,0,0,0.12);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  position: relative;
}

.qrcode-title {
  font-size: 32rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 30rpx;
  text-align: center;
}

.qrcode-image {
  width: 100%;
  max-width: 500rpx;
  margin: 10rpx 0;
  border-radius: 12rpx;
}

.qrcode-tips {
  font-size: 24rpx;
  color: #666;
  margin-top: 20rpx;
  text-align: center;
} 