// components/message-card/message-card.js
Component({
  properties: {
    item: {
      type: Object,
      value: {}
    },
    background: {
      type: String,
      value: "255, 255, 255"
    },
    index: {
      type: Number,
      value: 0
    }
  },

  data: {
    
  },

  methods: {
    viewMessageDetail() {
      this.triggerEvent('viewDetail', { 
        id: this.properties.item.id,
        index: this.properties.index
      });
    },
    
    dolike(e) {
      // 修复：在某些情况下 e 可能不是标准事件对象
      // 只有当 e 是标准事件对象时才阻止冒泡
      if (e && typeof e.stopPropagation === 'function') {
        e.stopPropagation();
      }
      
      this.triggerEvent('dolike', {
        id: this.properties.item.id,
        index: this.properties.index
      });
    },
    
    handleLongPress(e) {
      this.triggerEvent('longpress', {
        id: this.properties.item.id,
        userId: this.properties.item.user_id,
        index: this.properties.index
      });
    }
  }
}) 