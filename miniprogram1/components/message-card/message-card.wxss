/* components/message-card/message-card.wxss */
.message-card {
  box-sizing: border-box;
  border-radius: 34rpx;
  margin: 35rpx 20rpx;
  padding: 20rpx 25rpx 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  width: calc(100% - 40rpx);
  background-color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: auto;
  height: auto !important;
  min-width: 0;
  flex: 1;
  overflow: visible;
}

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 15rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  height: 70rpx;
  width: 70rpx;
  border-radius: 15%;
}

.username-container {
  display: flex;
  align-items: center;
}

.username {
  margin-left: 15rpx;
}

.username-location {
  display: flex;
  flex-direction: column;
}

.location {
  margin-left: 15rpx;
  font-size: 24rpx;
  color: #808080;
}

.title {
  margin-left: 10rpx;
  padding: 2rpx 8rpx;
  border-radius: 10rpx;
  font-size: 20rpx;
  color: white;
}

.category {
  font-size: 26rpx;
  color: #ecba16;
  margin-left: 15rpx;
}

.text {
  font-size: 32rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: pre-wrap;
  padding: 0;
  width: 100%;
}

.touxiang1 {
  margin-bottom: 15rpx;
  height: 70rpx;
  display: flex;
  background-color: transparent;
  border: none;
  align-items: flex-start;
  width: 100%;
}

.image-container {
  margin-top: 8rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
  width: 100%;
}

.image-item {
  margin-left: 0rpx;
  flex: 0 0 auto;
  margin-right: 18rpx;
}

.uniform-image {
  width: 198rpx;
  height: 198rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.kuang {
  height: 60rpx;
  margin-top: auto;
  display: flex;
  align-items: center;
  width: 100%;
}

.last {
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 35rpx;
  line-height: 35rpx;
}

.gradient-text {
  font-size: 24rpx;
  color: #808080;
  line-height: 35rpx;
}

.vote-container {
  margin: 0;
  padding: 0;
  background-color: transparent;
  position: relative;
  width: 100%;
}

.vote-header {
  margin-bottom: 0;
}

.vote-title-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.vote-title-icon {
  width: 28rpx;
  height: 28rpx;
}

.vote-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.vote-type-tag {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  display: inline-flex;
  align-items: center;
  height: fit-content;
  line-height: 1.4;
  box-shadow: 0 2rpx 6rpx rgba(255, 165, 0, 0.2);
}

.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 10rpx 0;
  margin-top: 20rpx;
}

.touxian2 {
  padding: 7rpx;
  margin-left: 15rpx;
  color: #ffffff;
  font-size: 20rpx;
  border-radius: 8rpx;
  line-height: 1;
  font-weight: bold;
  text-shadow: 0 1rpx 2rpx rgba(0, 0, 0, 0.2);
}

.footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-top: 10rpx;
  padding-top: 10rpx;
  border-top: 1rpx solid rgba(0, 0, 0, 0.05);
}

.timestamp {
  font-size: 24rpx;
  color: #666;
  line-height: 50rpx;
}

.actions {
  display: flex;
  align-items: center;
  font-size: 25rpx;
}

.price {
  color: rgb(226, 114, 23);
  margin-right: 10rpx;
}

.action-item {
  display: flex;
  align-items: center;
}

.icon {
  width: 35rpx;
  height: 35rpx;
  margin-right: 10rpx;
}

/* 删除本地背景色定义，使用app.wxss中的全局样式 */
/* .bg0 { background-color: #f27c7c; }
.bg1 { background-color: #7cb9e8; }
.bg2 { background-color: #7cc576; }
.bg3 { background-color: #f7a278; }
.bg4 { background-color: #9d7cf4; }
.bg5 { background-color: #f49ac2; } */ 