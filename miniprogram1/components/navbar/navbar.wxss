.custom-nav-bar {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  background-color: rgb(244, 244, 249);
  z-index: 999;
}

/* 为热门消息页面添加特殊的导航栏背景 */
/* .hot-page .custom-nav-bar {
  background-color: rgb(244, 244, 249);
} */

.nav-bar-content {
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  border-bottom: 1px solid #eee;
}

.left {
  width: 32px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-start;
  margin-left: 12px;
}

.right {
  width: 32px;
  height: 44px;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  margin-right: 12px;
}

.back-icon {
  width: 44rpx;
  height: 44rpx;
}

.center {
  flex: 1;
  display: flex;
  justify-content: center;
  align-items: center;
}

.title {
  font-size: 34rpx;
  font-weight: 500;
  color: #000;
}

.placeholder {
  width: 44rpx;
  height: 44rpx;
  opacity: 0;
}

.nav-bar-holder {
  width: 100%;
} 