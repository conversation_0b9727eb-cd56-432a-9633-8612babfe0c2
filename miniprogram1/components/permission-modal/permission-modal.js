Component({
  /**
   * 组件的属性列表
   */
  properties: {
    show: {
      type: Boolean,
      value: false
    },
    title: {
      type: String,
      value: '权限提示'
    },
    content: {
      type: String,
      value: '只有社团管理员可以添加二维码，如果您是社团人员或者官方组织，请联系Wenroujun1，会给您管理员身份再添加。'
    },
    buttonText: {
      type: String,
      value: '知道了'
    }
  },

  /**
   * 监听属性变化
   */
  observers: {
    'show': function(newVal) {
      if (newVal) {
        // 当show变为true时，执行showModal逻辑
        this.showModal();
      }
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    animation: null
  },

  /**
   * 组件的方法列表
   */
  methods: {
    // 显示弹窗
    showModal: function() {
      // 创建动画实例
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease',
      });
      
      // 设置初始状态（在屏幕底部）
      animation.translateY(300).step();
      this.setData({
        animation: animation.export()
      });
      
      // 延迟一下，让初始状态先渲染
      setTimeout(() => {
        // 设置最终状态（弹出）
        animation.translateY(0).step();
        this.setData({
          animation: animation.export()
        });
      }, 50);
    },
    
    // 关闭弹窗
    closeModal: function() {
      // 创建动画实例
      const animation = wx.createAnimation({
        duration: 300,
        timingFunction: 'ease',
      });
      
      // 设置最终状态（滑回屏幕底部）
      animation.translateY(300).step();
      this.setData({
        animation: animation.export()
      });
      
      // 延迟一下，等动画结束后再隐藏
      setTimeout(() => {
        this.setData({
          show: false
        });
        // 触发关闭事件，通知父组件
        this.triggerEvent('close');
      }, 300);
    },
    
    // 点击遮罩层关闭
    onMaskTap: function() {
      this.closeModal();
    },
    
    // 点击按钮关闭
    onButtonTap: function() {
      this.closeModal();
    }
  }
}) 