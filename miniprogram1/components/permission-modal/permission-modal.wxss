/* components/permission-modal/permission-modal.wxss */

/* 弹窗遮罩层 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

/* 权限提示弹窗样式 */
.permission-modal {
  position: fixed;
  bottom: 0;
  left: 0;
  width: 100%;
  background-color: #fff;
  border-top-left-radius: 30rpx;
  border-top-right-radius: 30rpx;
  z-index: 1001;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.1);
  overflow: hidden;
}

.modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  padding: 10rpx;
}

.modal-content {
  padding: 40rpx 30rpx;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.modal-footer {
  padding: 20rpx 30rpx 40rpx;
}

.modal-btn {
  width: 100%;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: #1aad19;
  color: #fff;
  border-radius: 40rpx;
  font-size: 30rpx;
}

.modal-btn::after {
  border: none;
} 