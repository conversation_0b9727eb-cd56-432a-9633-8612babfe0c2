Component({
  properties: {
    visible: {
      type: Boolean,
      value: false
    }
  },
  data: {
    // 组件内部数据
  },
  methods: {
    // 外卖代取 - 暂未开放
    goToPublishPaotui() {
      wx.showToast({
        title: '暂未开放',
        icon: 'none',
        duration: 2000
      });
    },
    
    // 二手闲置
    goToPublishErShou() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=3',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 打听求助
    goToPublishQiuzhu() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=4',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 发条说说
    goToPublishWeituo() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=2',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 寻找搭子
    goToPublishLianai() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=99',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 拼车交流
    goToPublishGuatian() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=5',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 失物寻找
    goToPublishJianzhi() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=6',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 租房求助
    goToPublishZulin() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=71',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 房屋出租
    goToPublishChuzu() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=72',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 赚点外快
    goToPublishXiaoyuan() {
      wx.navigateTo({
        url: '/pages/foldshare/publish/publish?type=8',
        success: () => {
          this.triggerEvent('close');
        }
      });
    },
    
    // 关闭弹窗
    onClose() {
      this.triggerEvent('close');
    },
    
    // 阻止冒泡，防止点击内容区域关闭弹窗
    stopPropagation() {
      return false;
    },
    
    // 阻止遮罩层滑动
    preventTouchMove() {
      return false;
    }
  }
}) 