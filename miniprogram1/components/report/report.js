const { navigateBack } = require('../../utils/navigation');

Component({
  properties: {
    postId: {
      type: String,
      value: ''
    }
  },

  data: {
    reportCount: 0,
    isDeleted: false,
    showReportDialog: false,
    showProgressDialog: false
  },

  methods: {
    showReport() {
      this.setData({
        showReportDialog: true
      });
    },

    hideReport() {
      this.setData({
        showReportDialog: false
      });
    },

    async confirmReport() {
      const userId = wx.getStorageSync('user_id');
      const accessToken = wx.getStorageSync('access_token');
      if (!userId || !accessToken) {
        wx.showToast({
          title: '请先登录',
          icon: 'none'
        });
        return;
      }
      try {
        wx.request({
          url: getApp().globalData.wangz + '/report/report',
          method: 'POST',
          header: {
            'token': accessToken
          },
          data: {
            post_id: this.properties.postId
          },
          success: (res) => {
            if (res.data.code === 200) {
              this.setData({
                reportCount: res.data.data.report_count,
                isDeleted: res.data.data.is_deleted,
                showReportDialog: false
              });
              wx.showToast({
                title: res.data.msg,
                icon: 'success'
              });
              if (res.data.data.is_deleted) {
                setTimeout(() => {
                  const pages = getCurrentPages();
                  if (pages.length > 1) {
                    wx.navigateBack();
                  } else {
                    wx.reLaunch({ url: '/pages/fold1/home/<USER>' });
                  }
                }, 1200);
              }
            } else {
              wx.showToast({
                title: res.data.msg,
                icon: 'none'
              });
            }
          },
          fail: () => {
            wx.showToast({
              title: '网络错误',
              icon: 'none'
            });
          }
        });
      } catch (error) {
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        });
      }
    },

    async getReportCount() {
      const userId = wx.getStorageSync('user_id');
      const accessToken = wx.getStorageSync('access_token');
      try {
        wx.request({
          url: getApp().globalData.wangz + '/report/getReportCount',
          method: 'POST',
          data: {
            post_id: this.properties.postId,
            userid: userId,
            access_token: accessToken
          },
          success: (res) => {
            if (res.data.code === 200) {
              this.setData({
                reportCount: res.data.data.report_count,
                isDeleted: res.data.data.is_deleted
              });
            }
          },
          fail: (error) => {
            console.error('获取举报数量失败', error);
          }
        });
      } catch (error) {
        console.error('获取举报数量失败', error);
      }
    },

    hideProgressDialog() {
      this.setData({ showProgressDialog: false });
    },

    showReportDialog(postId) {
      wx.showModal({
        title: '确认举报',
        content: '确定要举报该内容吗？',
        confirmColor: '#FF4D4F',
        success: (res) => {
          if (res.confirm) {
            const accessToken = wx.getStorageSync('access_token');
            if (!accessToken) {
              wx.showToast({
                title: '请先登录',
                icon: 'none'
              });
              return;
            }
            wx.request({
              url: getApp().globalData.wangz + '/report/report',
              method: 'POST',
              header: {
                'token': accessToken
              },
              data: {
                post_id: postId
              },
              success: (res) => {
                if (res.data.code === 200) {
                  this.setData({
                    reportCount: res.data.data.report_count,
                    showProgressDialog: true
                  });
                  if (res.data.data.is_deleted) {
                    setTimeout(() => {
                      const pages = getCurrentPages();
                      if (pages.length > 1) {
                        wx.navigateBack();
                      } else {
                        wx.reLaunch({ url: '/pages/fold1/home/<USER>' });
                      }
                    }, 1200);
                  }
                } else {
                  wx.showToast({
                    title: res.data.msg,
                    icon: 'none'
                  });
                }
              },
              fail: () => {
                wx.showToast({
                  title: '网络错误',
                  icon: 'none'
                });
              }
            });
          }
        }
      });
    },

    handleSuccess() {
      navigateBack();
    },

    handleCancel() {
      navigateBack();
    }
  },

  lifetimes: {
    attached() {
      this.getReportCount();
    }
  }
}); 