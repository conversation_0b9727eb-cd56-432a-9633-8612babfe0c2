.report-container {
  width: 100%;
  padding: 20rpx;
}

.report-button {
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: #666;
  font-size: 28rpx;
}

.report-icon {
  width: 32rpx;
  height: 32rpx;
}

.report-progress {
  margin-top: 20rpx;
}

.progress-bar {
  width: 100%;
  height: 20rpx;
  background-color: #f0f0f0;
  border-radius: 10rpx;
  overflow: hidden;
}

.progress-inner {
  height: 100%;
  background-color: #ff4d4f;
  transition: width 0.3s ease;
}

.progress-text {
  margin-top: 10rpx;
  font-size: 24rpx;
  color: #666;
}

.report-dialog {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.dialog-mask {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
}

.dialog-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 80%;
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx;
}

.dialog-title {
  font-size: 32rpx;
  font-weight: bold;
  text-align: center;
  margin-bottom: 30rpx;
}

.dialog-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
}

.dialog-buttons {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
}

.cancel-btn, .confirm-btn {
  flex: 1;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.cancel-btn {
  background-color: #f0f0f0;
  color: #666;
}

.confirm-btn {
  background-color: #ff4d4f;
  color: #fff;
}

.report-component {
  display: none;
}

.progress-dialog-mask {
  position: fixed;
  left: 0; top: 0; right: 0; bottom: 0;
  background: rgba(0,0,0,0.4);
  z-index: 9999;
  display: flex;
  align-items: center;
  justify-content: center;
}
.progress-dialog {
  background: #fff;
  border-radius: 20rpx;
  padding: 48rpx 36rpx;
  width: 80vw;
  max-width: 500rpx;
  text-align: center;
}
.progress-title {
  font-size: 32rpx;
  font-weight: bold;
  margin-bottom: 32rpx;
}
.progress-bar-outer {
  width: 100%;
  height: 18rpx;
  background: #f0f0f0;
  border-radius: 9rpx;
  margin-bottom: 24rpx;
  overflow: hidden;
}
.progress-bar-inner {
  height: 100%;
  background: #ff4d4f;
  border-radius: 9rpx;
  transition: width 0.3s;
}
.progress-text {
  font-size: 26rpx;
  color: #666;
  margin-bottom: 32rpx;
}
.progress-close-btn {
  width: 100%;
  height: 80rpx;
  background: #ff4d4f;
  color: #fff;
  border-radius: 40rpx;
  font-size: 28rpx;
}

.progress-message {
  font-size: 26rpx;
  color: #6b7280;
  margin-bottom: 24rpx;
  text-align: center;
  line-height: 1.7;
}

/* 兼容group-notice的modal-main-btn按钮风格 */
.modal-main-btn {
  width: 60%;
  border-radius: 999rpx;
  padding: 14rpx 0;
  text-align: center;
  font-size: 28rpx;
  font-weight: 700;
  border: 3rpx solid #222;
  background: #fff;
  color: #222;
  margin-top: 0;
  margin-bottom: 0;
  letter-spacing: 2rpx;
  transition: background 0.2s, border-color 0.2s;
}
.modal-main-btn:active {
  background: #f2f2f2;
  border-color: #111;
} 