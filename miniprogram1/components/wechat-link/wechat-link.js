Component({
  properties: {
    // 可以通过属性传入配置，如果不传则自动获取
    externalConfig: {
      type: Object,
      value: {}
    }
  },

  data: {
    config: {
      show_wechat_link: false,
      wechat_url: '',
      wechat_image: '',
      wechat_title: ''
    }
  },

  lifetimes: {
    attached() {
      // 如果没有传入配置，则自动获取
      if (!this.properties.externalConfig || Object.keys(this.properties.externalConfig).length === 0) {
        this.loadWechatConfig()
      } else {
        this.setData({
          config: this.properties.externalConfig
        })
      }
    }
  },

  methods: {
    // 获取公众号配置
    loadWechatConfig() {
      wx.request({
        url: getApp().globalData.wangz + '/wechatConfig/getConfig',
        method: 'POST',
        header: {
          'content-type': 'application/x-www-form-urlencoded'
        },
        success: (res) => {
          if (res.data.code === 200) {
            this.setData({
              config: res.data.data
            })
          } else {
            console.error('获取公众号配置失败:', res.data.msg)
          }
        },
        fail: (err) => {
          console.error('获取公众号配置失败:', err)
        }
      })
    },

    // 打开公众号文章
    openWechatArticle() {
      const url = this.data.config.wechat_url
      if (!url) {
        wx.showToast({
          title: '链接不存在',
          icon: 'none'
        })
        return
      }

      // 使用web-view跳转到公众号文章
      wx.navigateTo({
        url: `/pages/webview/webview?url=${encodeURIComponent(url)}`,
        fail: (err) => {
          console.error('跳转失败:', err)
          wx.showToast({
            title: '跳转失败',
            icon: 'none'
          })
        }
      })
    }
  }
})
