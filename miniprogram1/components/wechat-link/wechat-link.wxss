.wechat-link-container {
  margin: 0 20rpx;
  margin-bottom: 30rpx;
  background: #fff;
  border-radius: 16rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

.wechat-link-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
}

.wechat-image {
  width: 100%;
  height: 300rpx;
  background-color: #f5f5f5;
  display: block;
}

.wechat-title {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  font-weight: 700;
  padding: 20rpx 30rpx 30rpx 30rpx;
}

/* 点击效果 */
.wechat-link-content:active {
  background-color: #f8f8f8;
  transform: scale(0.98);
  transition: all 0.1s ease;
}
