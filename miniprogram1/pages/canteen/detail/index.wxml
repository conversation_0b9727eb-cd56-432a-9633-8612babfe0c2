<view class="canteen-bg">
  <custom-nav-bar title="{{canteenName}}" back="true"></custom-nav-bar>
  <loading show="{{isLoading}}" mask="false"></loading>
  <view wx:if="{{!isLoading}}">
    <!-- 楼层tab栏 -->
    <view wx:if="{{floors.length <= 5}}" class="tab-bar-row single-row">
      <view class="tab-bar-item {{currentFloor==item?'active':''}}"
            wx:for="{{floors}}" wx:key="*this"
            bindtap="switchFloor" data-floor="{{item}}">
        {{item}}
      </view>
    </view>
    <view wx:elif="{{floors.length == 6}}" class="tab-bar-row double-row">
      <view class="tab-bar-item {{currentFloor==item?'active':''}}"
            wx:for="{{floors}}" wx:key="*this"
            bindtap="switchFloor" data-floor="{{item}}">
        {{item}}
      </view>
    </view>
    <scroll-view wx:else class="tab-bar-scroll" scroll-x="true" enable-flex="true">
      <view class="tab-bar-item {{currentFloor==item?'active':''}}"
            wx:for="{{floors}}" wx:key="*this"
            bindtap="switchFloor" data-floor="{{item}}">
        {{item}}
      </view>
    </scroll-view>

    <!-- 窗口列表 - 使用与餐厅列表相同的样式 -->
    <view class="msg-list">
      <view class="canteen-card" wx:for="{{windowList}}" wx:key="id" bindtap="goToWindowDetail" data-id="{{item.id}}">
        <view class="canteen-avatar large-avatar">
          <image class="avatar-img" src="{{item.img || '/images/shitang.png'}}" mode="aspectFill" catchtap="previewWindowAvatar" data-img="{{item.img}}"></image>
        </view>
        <view class="card-info adjust-info">
          <view class="card-header-row">
            <text class="canteen-name">{{item.name}}</text>
            <view class="total-rating-card">
              <image src="/images/xingxingx.png" class="star-icon"></image>
              <text>{{item.formatted_rating || '0.0'}}</text>
            </view>
          </view>
          <view class="canteen-second-row">
            <view class="hot-comment" wx:if="{{item.hot_comment}}">"{{item.hot_comment}}"</view>
            <view class="hot-comment" wx:else>暂无评论</view>
            <text class="score-people-text">{{item.total_comment_count || 0}}人已评分</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 右下角悬浮添加按钮 - 仅管理员可见 -->
    <fixed-add-btn wx:if="{{canManageCanteen}}" bind:add="showAddWindowModal" />
  </view>

  <!-- 添加窗口弹窗 -->
  <view class="modal {{showAddWindowModal?'show':''}}" wx:if="{{showAddWindowModal}}">
    <view class="modal-mask" bindtap="hideAddWindowModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">添加窗口</view>
      </view>
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">窗口名称</text>
          <input class="modal-input" placeholder="请输入窗口名称" value="{{newWindow.name}}" bindinput="onWindowNameInput" />
        </view>
        <view class="input-group">
          <text class="input-label">上传头像</text>
          <view class="upload-btn" bindtap="chooseWindowAvatar">
            <image src="{{newWindow.avatar || '/images/xiangji.png'}}" mode="aspectFit"></image>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideAddWindowModal">取消</button>
        <button class="btn-confirm" bindtap="addWindow">确定</button>
      </view>
    </view>
  </view>
</view>