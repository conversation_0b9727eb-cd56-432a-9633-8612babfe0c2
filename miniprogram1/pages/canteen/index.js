const app = getApp()

Page({
  data: {
    avatarUrl: '',
    canteenName: '',
    floorOptions: [
      { floor: '-3层', selected: false },
      { floor: '-2层', selected: false },
      { floor: '-1层', selected: false },
      { floor: '0层', selected: false },
      { floor: '1层', selected: false },
      { floor: '2层', selected: false },
      { floor: '3层', selected: false },
      { floor: '4层', selected: false },
      { floor: '5层', selected: false },
      { floor: '6层', selected: false }
    ],
    selectedFloorCount: 0,
    canSubmit: false,
    isSubmitting: false
  },

  onLoad() {
    // 页面加载时的初始化
  },

  // 选择头像
  chooseAvatar() {
    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['album', 'camera'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFilePath = res.tempFiles[0].tempFilePath
        this.setData({
          avatarUrl: tempFilePath
        })
        this.checkCanSubmit()
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 输入餐厅名称
  onNameInput(e) {
    this.setData({
      canteenName: e.detail.value
    })
    this.checkCanSubmit()
  },

  // 切换楼层选择
  toggleFloor(e) {
    const index = e.currentTarget.dataset.index
    let floorOptions = [...this.data.floorOptions]

    // 如果当前是未选中状态，要选中的话，需要检查是否超过限制
    if (!floorOptions[index].selected) {
      const selectedCount = floorOptions.filter(item => item.selected).length
      if (selectedCount >= 6) {
        wx.showToast({
          title: '最多只能选择6个楼层',
          icon: 'none'
        })
        return
      }
    }

    // 切换选中状态
    floorOptions[index].selected = !floorOptions[index].selected

    // 计算已选择的楼层数量
    const selectedFloorCount = floorOptions.filter(item => item.selected).length

    this.setData({
      floorOptions,
      selectedFloorCount
    })
    this.checkCanSubmit()
  },

  // 检查是否可以提交
  checkCanSubmit() {
    const { avatarUrl, canteenName, floorOptions } = this.data
    const selectedFloors = floorOptions.filter(item => item.selected)
    const canSubmit = avatarUrl && canteenName.trim() && selectedFloors.length > 0
    this.setData({
      canSubmit
    })
  },

  // 提交表单
  submitForm() {
    if (!this.data.canSubmit || this.data.isSubmitting) {
      return
    }

    const { avatarUrl, canteenName, floorOptions } = this.data
    const selectedFloors = floorOptions.filter(item => item.selected).map(item => item.floor)

    // 表单验证
    if (!avatarUrl) {
      wx.showToast({
        title: '请上传餐厅头像',
        icon: 'none'
      })
      return
    }

    if (!canteenName.trim()) {
      wx.showToast({
        title: '请输入餐厅名称',
        icon: 'none'
      })
      return
    }

    if (selectedFloors.length === 0) {
      wx.showToast({
        title: '请选择至少一个楼层',
        icon: 'none'
      })
      return
    }

    // 获取token
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    this.setData({ isSubmitting: true })

    // 显示加载提示
    wx.showLoading({
      title: '上传头像中...',
      mask: true
    })

    // 先上传头像
    wx.uploadFile({
      url: getApp().globalData.wangz + '/upload/uploadCanteenAvatar',
      filePath: avatarUrl,
      name: 'avatar',
      header: {
        'token': token
      },
      success: (uploadRes) => {
        try {
          const uploadData = JSON.parse(uploadRes.data)
          console.log('头像上传响应:', uploadData)

          if (uploadData.code === 200) {
            // 头像上传成功，提交表单
            wx.showLoading({
              title: '发布中...',
              mask: true
            })

            this.submitCanteenData(canteenName, selectedFloors, uploadData.data.url, token)
          } else {
            wx.hideLoading()
            wx.showToast({
              title: uploadData.msg || '头像上传失败',
              icon: 'none'
            })
            this.setData({ isSubmitting: false })
          }
        } catch (err) {
          wx.hideLoading()
          console.error('解析头像上传响应失败:', err)
          wx.showToast({
            title: '头像上传失败',
            icon: 'none'
          })
          this.setData({ isSubmitting: false })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('头像上传失败:', err)
        wx.showToast({
          title: '头像上传失败，请重试',
          icon: 'none'
        })
        this.setData({ isSubmitting: false })
      }
    })
  },

  // 提交食堂数据
  submitCanteenData(canteenName, selectedFloors, imageUrl, token) {
    wx.request({
      url: getApp().globalData.wangz + '/canteen/add',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        name: canteenName.trim(),
        floors: JSON.stringify(selectedFloors),
        description: '新添加的食堂',
        location: '待完善',
        image_url: imageUrl
      },
      success: (res) => {
        wx.hideLoading()
        console.log('添加食堂响应:', res.data)
        if (res.data.code === 200) {
          wx.showToast({
            title: '发布成功',
            icon: 'success'
          })
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.data.msg || '发布失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('添加失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ isSubmitting: false })
      }
    })
  }
})
