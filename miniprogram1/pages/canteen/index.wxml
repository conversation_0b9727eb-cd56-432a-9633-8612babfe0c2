<view class="canteen-bg">
  <custom-nav-bar title="发布餐厅" back="true"></custom-nav-bar>

  <view class="content">
    <view class="form-container">
      <!-- 上传头像 -->
      <view class="form-section">
        <view class="section-title">餐厅头像</view>
        <view class="avatar-upload" bindtap="chooseAvatar">
          <image
            class="avatar-img"
            src="{{avatarUrl || '/images/shitang.png'}}"
            mode="aspectFill"
          ></image>
          <view class="upload-overlay">
            <image class="camera-icon" src="/images/xiangji.png" mode="aspectFit"></image>
            <text class="upload-text">{{avatarUrl ? '更换头像' : '上传头像'}}</text>
          </view>
        </view>
      </view>

      <!-- 餐厅名称 -->
      <view class="form-section">
        <view class="section-title">餐厅名称</view>
        <view class="input-container">
          <input
            class="name-input"
            placeholder="请输入餐厅名称"
            value="{{canteenName}}"
            bindinput="onNameInput"
            maxlength="20"
          />
          <text class="char-count">{{canteenName.length}}/20</text>
        </view>
      </view>

      <!-- 选择楼层 -->
      <view class="form-section">
        <view class="section-title">选择楼层</view>
        <view class="floor-grid">
          <view
            class="floor-item {{item.selected ? 'selected' : ''}}"
            wx:for="{{floorOptions}}"
            wx:key="floor"
            bindtap="toggleFloor"
            data-index="{{index}}"
          >
            {{item.floor}}
          </view>
        </view>
        <view class="floor-tip {{selectedFloorCount >= 5 ? 'warning' : ''}}">
          请选择餐厅包含的楼层（最多6个，已选<text class="count">{{selectedFloorCount}}</text>个）
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <view
        class="submit-btn {{canSubmit ? 'active' : 'disabled'}}"
        bindtap="submitForm"
      >
        发布餐厅
      </view>
    </view>
  </view>
</view>
