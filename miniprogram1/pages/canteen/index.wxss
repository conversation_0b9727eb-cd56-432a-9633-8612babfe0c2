.canteen-bg {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 24rpx 0;
}

.content {
  padding: 0 32rpx;
}

.form-container {
  background: #fff;
  border-radius: 20rpx;
  padding: 40rpx 32rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  margin-bottom: 40rpx;
}

.form-section {
  margin-bottom: 48rpx;
}

.form-section:last-child {
  margin-bottom: 0;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 头像上传样式 */
.avatar-upload {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
  margin: 0 auto;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.avatar-upload:active {
  transform: scale(0.98);
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.upload-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.4);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
}

.camera-icon {
  width: 48rpx;
  height: 48rpx;
  opacity: 0.9;
}

.upload-text {
  color: #fff;
  font-size: 24rpx;
  font-weight: 500;
}

/* 名称输入样式 */
.input-container {
  position: relative;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 24rpx 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.input-container:focus-within {
  border-color: #5ec6fa;
  background: #fff;
  box-shadow: 0 0 0 6rpx rgba(94,198,250,0.1);
}

.name-input {
  font-size: 30rpx;
  color: #333;
  width: 100%;
  padding-right: 80rpx;
}

.name-input::placeholder {
  color: #999;
}

.char-count {
  position: absolute;
  right: 32rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 24rpx;
  color: #999;
}

/* 楼层选择样式 */
.floor-grid {
  display: grid;
  grid-template-columns: repeat(5, 1fr);
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.floor-item {
  height: 80rpx;
  background: #f8f9fa;
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  cursor: pointer;
}

.floor-item:active {
  transform: scale(0.95);
}

.floor-item.selected {
  background: #5ec6fa !important;
  color: #fff !important;
  border-color: #5ec6fa !important;
  box-shadow: 0 4rpx 12rpx rgba(94,198,250,0.3) !important;
  font-weight: 600 !important;
  transform: scale(1.02) !important;
}

.floor-tip {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  transition: color 0.3s ease;
}

.floor-tip.warning {
  color: #ff6b6b;
}

.floor-tip .count {
  color: #5ec6fa;
  font-weight: 600;
}

.floor-tip.warning .count {
  color: #ff6b6b;
}

/* 提交按钮样式 */
.submit-container {
  padding: 0 32rpx 40rpx 32rpx;
}

.submit-btn {
  width: 100%;
  height: 96rpx;
  border-radius: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  transition: all 0.3s ease;
  letter-spacing: 2rpx;
}

.submit-btn.active {
  background: linear-gradient(90deg, #5ec6fa 0%, #3487ef 100%);
  color: #fff;
  box-shadow: 0 8rpx 24rpx rgba(52,135,239,0.3);
}

.submit-btn.active:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(52,135,239,0.2);
}

.submit-btn.disabled {
  background: #e9ecef;
  color: #adb5bd;
  cursor: not-allowed;
}
