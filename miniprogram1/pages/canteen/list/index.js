const app = getApp()
import roleManager from '../../../utils/roleManager.js'

function getFloorNum(name) {
  if (!name) return '';
  // 处理B1、B2等负楼层
  if (/^B(\d+)/i.test(name)) {
    return '-' + name.replace(/[^\d]/g, '');
  }
  // 提取数字
  const num = name.replace(/[^\d]/g, '');
  return num || name;
}

function groupFloors(floorList) {
  // 预处理每个楼层对象，增加floorNum字段
  const newFloors = floorList.map(f => ({
    ...f,
    floorNum: getFloorNum(f.name)
  }));
  const len = newFloors.length;
  if (len === 2) {
    // 1*2
    const arr = [newFloors[0] || null, newFloors[1] || null];
    return [arr];
  } else if (len === 3 || len === 4) {
    // 2*2
    const arr = [newFloors[0] || null, newFloors[1] || null, newFloors[2] || null, newFloors[3] || null];
    return [arr.slice(0,2), arr.slice(2,4)];
  } else if (len > 4) {
    // 2*3
    const arr = [
      newFloors[0] || null, newFloors[1] || null, newFloors[2] || null,
      newFloors[3] || null, newFloors[4] || null, newFloors[5] || null
    ];
    return [arr.slice(0,3), arr.slice(3,6)];
  } else {
    // 1个或0个
    return [[newFloors[0] || null]];
  }
}

function splitCommentToTwoLines(comment, maxLen1 = 18, maxLen2 = 18, secondLineEllipsis = 9) {
  if (!comment) return { comment_line1: '', comment_line2: '' };
  const line1 = comment.slice(0, maxLen1);
  let line2 = comment.slice(maxLen1, maxLen1 + maxLen2);
  if (line2.length > secondLineEllipsis) {
    line2 = line2.slice(0, secondLineEllipsis) + '...';
  }
  return { comment_line1: line1, comment_line2: line2 };
}

Page({
  data: {
    canteenList: [],
    isLoading: true,
    loginRetryCount: 0,
    maxLoginRetries: 5,
    needRefresh: false,
    canManageCanteen: false, // 是否可以管理食堂

    // 抽取今日美食相关
    showModal: false,
    showResult: false,
    showDropdown: false,
    showRatingDropdown: false,
    selectedCanteenIds: [1, 2], // 默认沙河校区
    selectedCanteenName: '沙河校区',
    minRating: -10,
    minRatingText: '-10分',
    ratingOptions: [
      { value: 10, text: '10分' },
      { value: 9, text: '9分' },
      { value: 8, text: '8分' },
      { value: 7, text: '7分' },
      { value: 6, text: '6分' },
      { value: 5, text: '5分' },
      { value: 4, text: '4分' },
      { value: 3, text: '3分' },
      { value: 2, text: '2分' },
      { value: 1, text: '1分' },
      { value: 0, text: '0分' },
      { value: -1, text: '-1分' },
      { value: -2, text: '-2分' },
      { value: -3, text: '-3分' },
      { value: -4, text: '-4分' },
      { value: -5, text: '-5分' },
      { value: -6, text: '-6分' },
      { value: -7, text: '-7分' },
      { value: -8, text: '-8分' },
      { value: -9, text: '-9分' },
      { value: -10, text: '-10分' }
    ],
    isExtracting: false,
    resultWindow: null,
    resultAnimation: false
  },

  onLoad() {
    // 检查用户权限
    this.checkPermissions()
    // 检查用户登录状态
    this.checkLoginAndLoadData()
    // 加载用户上次的选择
    this.loadUserPreferences()
  },

  onShow() {
    // 只有在数据为空或者明确需要刷新时才重新加载
    if (!this.data.canteenList || this.data.canteenList.length === 0 || this.data.needRefresh) {
      this.checkLoginAndLoadData()
      this.setData({ needRefresh: false })
    }
  },

  // 检查用户权限
  checkPermissions() {
    const canManageCanteen = roleManager.canManageCanteen()
    this.setData({ canManageCanteen })
  },

  // 检查登录状态并加载数据
  checkLoginAndLoadData() {
    // 检查是否有token和用户ID
    const token = wx.getStorageSync('access_token')
    const userId = wx.getStorageSync('user_id')

    if (!token || !userId) {
      // 检查重试次数
      if (this.data.loginRetryCount >= this.data.maxLoginRetries) {
        console.log('[食堂列表] 登录重试次数已达上限')
        wx.showToast({
          title: '登录超时，请重新进入',
          icon: 'none',
          duration: 2000
        })
        this.setData({ isLoading: false })
        return
      }

      // 增加重试次数
      this.setData({
        loginRetryCount: this.data.loginRetryCount + 1
      })

      // 没有token，显示提示并延迟重试
      console.log(`[食堂列表] 没有token，等待登录完成 (${this.data.loginRetryCount}/${this.data.maxLoginRetries})`)
      wx.showToast({
        title: '正在登录中...',
        icon: 'loading',
        duration: 1500
      })

      // 延迟1.5秒后重试
      setTimeout(() => {
        this.checkLoginAndLoadData()
      }, 1500)
      return
    }

    // 有token，重置重试次数并加载数据
    console.log('[食堂列表] 检测到token，开始加载数据')
    this.setData({ loginRetryCount: 0 })
    this.getCanteenList()
  },

  // 获取食堂列表
  getCanteenList() {
    this.setData({ isLoading: true })

    const token = wx.getStorageSync('access_token')

    wx.request({
      url: getApp().globalData.wangz + '/canteen/getList',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 处理后端返回的数据格式
          const canteenList = res.data.data.map(canteen => {
            // 转换为前端需要的格式
            const floorList = canteen.floors.map(floor => {
              const floorRating = canteen.ratings.find(r => r.floor === floor)
              return {
                name: floor,
                rating: floorRating ? floorRating.avg_rating : 0
              }
            })

            const floorGroups = groupFloors(floorList)
            const { comment_line1, comment_line2 } = splitCommentToTwoLines(canteen.description)

            // 处理图片URL
            let imageUrl = canteen.image_url || '/images/shitang.png'
            if (imageUrl.startsWith('/uploads/')) {
              // 如果是上传的图片，添加域名前缀
              imageUrl = getApp().globalData.wangz + imageUrl
            }

            // 格式化评分为一位小数
            const formattedRating = canteen.avg_rating ? parseFloat(canteen.avg_rating).toFixed(1) : '0.0'

            // 使用后端返回的统计数据
            const totalCommentCount = canteen.comment_count || 0
            const totalRatingCount = canteen.rating_count || 0
            const totalPeopleCount = canteen.total_people_count || (totalCommentCount + totalRatingCount)
            const hotComment = canteen.description || '暂无评论'

            return {
              id: canteen.id,
              name: canteen.name,
              img: imageUrl,
              total_rating: canteen.avg_rating,
              formatted_rating: formattedRating, // 添加格式化后的评分
              hot_comment: hotComment,
              comment_count: totalCommentCount,
              rating_count: totalRatingCount,
              score_count: totalPeopleCount,
              total_comment_count: totalPeopleCount,
              floors: canteen.floors.join(','),
              floorList: floorList,
              floorGroups: floorGroups,
              comment_line1: comment_line1,
              comment_line2: comment_line2
            }
          })

          // 按评分从高到低排序
          canteenList.sort((a, b) => {
            const ratingA = parseFloat(a.total_rating) || 0
            const ratingB = parseFloat(b.total_rating) || 0
            return ratingB - ratingA // 降序排列
          })

          this.setData({
            canteenList: canteenList,
            isLoading: false
          })
        } else {
          wx.showToast({
            title: res.data.msg || '获取失败',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      },
      fail: (err) => {
        console.error('获取食堂列表失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      }
    })
  },

  // 跳转到添加页面
  goToAddPage() {
    // 检查权限
    if (!roleManager.canManageCanteen()) {
      wx.showToast({
        title: '只有管理员可以发布食堂',
        icon: 'none',
        duration: 2000
      })
      return
    }

    wx.navigateTo({
      url: '/pages/canteen/index'
    })
  },

  // 跳转到详情页
  goToDetail(e) {
    const id = e.currentTarget.dataset.id
    const canteen = this.data.canteenList.find(item => item.id === id)
    if (canteen) {
      wx.navigateTo({
        url: `/pages/canteen/detail/index?id=${id}&name=${encodeURIComponent(canteen.name)}&floors=${encodeURIComponent(canteen.floors)}`
      })
    }
  },

  // 预览头像
  previewAvatar(e) {
    const imageUrl = e.currentTarget.dataset.img
    if (imageUrl) {
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl]
      })
    }
  },

  // 加载用户偏好设置
  loadUserPreferences() {
    const savedCanteenIds = wx.getStorageSync('extract_canteen_ids')
    const savedCanteenName = wx.getStorageSync('extract_canteen_name')
    const savedRating = wx.getStorageSync('extract_rating')

    if (savedCanteenIds && savedCanteenName) {
      this.setData({
        selectedCanteenIds: savedCanteenIds,
        selectedCanteenName: savedCanteenName
      })
    }
    if (savedRating !== '') {
      const rating = savedRating || -10
      const ratingText = rating + '分'
      this.setData({
        minRating: rating,
        minRatingText: ratingText
      })
    }
  },

  // 保存用户偏好设置
  saveUserPreferences() {
    wx.setStorageSync('extract_canteen_ids', this.data.selectedCanteenIds)
    wx.setStorageSync('extract_canteen_name', this.data.selectedCanteenName)
    wx.setStorageSync('extract_rating', this.data.minRating)
  },

  // 显示抽取弹窗
  showExtractModal() {
    this.setData({ showModal: true })
  },

  // 隐藏抽取弹窗
  hideModal() {
    this.setData({ showModal: false })
  },

  // 隐藏结果弹窗
  hideResult() {
    this.setData({
      showResult: false,
      resultAnimation: false
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 切换下拉列表
  toggleDropdown() {
    this.setData({
      showDropdown: !this.data.showDropdown,
      showRatingDropdown: false // 关闭评分下拉
    })
  },

  // 切换评分下拉列表
  toggleRatingDropdown() {
    this.setData({
      showRatingDropdown: !this.data.showRatingDropdown,
      showDropdown: false // 关闭食堂下拉
    })
  },

  // 选择食堂
  selectCanteen(e) {
    const ids = e.currentTarget.dataset.ids
    const name = e.currentTarget.dataset.name

    let canteenIds = []
    if (typeof ids === 'string') {
      canteenIds = ids.split(',').map(id => parseInt(id))
    } else if (typeof ids === 'number') {
      canteenIds = [ids]
    } else if (Array.isArray(ids)) {
      canteenIds = ids.map(id => parseInt(id))
    }

    this.setData({
      selectedCanteenIds: canteenIds,
      selectedCanteenName: name,
      showDropdown: false
    })
  },

  // 选择评分
  selectRating(e) {
    const value = e.currentTarget.dataset.value
    const text = e.currentTarget.dataset.text

    this.setData({
      minRating: parseInt(value),
      minRatingText: text,
      showRatingDropdown: false
    })
  },

  // 抽取美食
  extractFood() {
    if (this.data.isExtracting) return

    // 保存用户偏好
    this.saveUserPreferences()

    this.setData({ isExtracting: true })

    // 使用选择的食堂ID
    const canteenIds = this.data.selectedCanteenIds

    const token = wx.getStorageSync('access_token')

    wx.request({
      url: getApp().globalData.wangz + '/window/getList',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        canteen_ids: canteenIds,
        min_rating: this.data.minRating
      },
      success: (res) => {
        this.setData({ isExtracting: false })

        if (res.data.code === 200) {
          const windows = res.data.data
          if (windows.length === 0) {
            wx.showToast({
              title: `没有超过${this.data.minRating}分的窗口喔`,
              icon: 'none',
              duration: 2000
            })
            return
          }

          // 随机选择一个窗口
          const randomIndex = Math.floor(Math.random() * windows.length)
          const selectedWindow = windows[randomIndex]

          // 处理图片URL
          let avatarUrl = selectedWindow.avatar
          if (avatarUrl && avatarUrl.startsWith('/uploads/')) {
            avatarUrl = getApp().globalData.wangz + avatarUrl
          }

          // 显示抽取动画和结果
          this.setData({
            showModal: false,
            resultWindow: {
              ...selectedWindow,
              avatar: avatarUrl,
              rating: selectedWindow.rating.toFixed(1)
            }
          })

          // 延迟显示结果弹窗，增加期待感
          setTimeout(() => {
            this.setData({
              showResult: true,
              resultAnimation: true
            })
          }, 300)

        } else {
          wx.showToast({
            title: res.data.msg || '抽取失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        this.setData({ isExtracting: false })
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 再抽一次
  extractAgain() {
    this.setData({
      showResult: false,
      resultAnimation: false
    })
    // 直接使用当前选项重新抽取
    this.extractFood()
  },

  // 去看看窗口详情
  goToWindow() {
    if (!this.data.resultWindow) return

    const window = this.data.resultWindow
    this.setData({
      showResult: false,
      resultAnimation: false
    })

    wx.navigateTo({
      url: `/pages/window/detail/index?id=${window.id}&name=${encodeURIComponent(window.name)}`
    })
  }
})
