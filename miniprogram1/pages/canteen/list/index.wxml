<view class="canteen-bg">
  <custom-nav-bar title="食堂评分" back="true"></custom-nav-bar>
  <loading show="{{isLoading}}" mask="false"></loading>

  <view wx:if="{{!isLoading}}" class="content">
    <!-- 公众号链接组件 -->
    <wechat-link wx:if="{{canteenList.length > 0}}"></wechat-link>

    <!-- 空状态 -->
    <view wx:if="{{canteenList.length === 0}}" class="empty-state">
      <image src="/images/shitang.png" class="empty-icon" mode="aspectFit"></image>
      <view class="empty-text">暂无食堂，快去添加吧~</view>
      <view wx:if="{{canManageCanteen}}" class="empty-btn" bindtap="goToAddPage">添加食堂</view>
    </view>

    <!-- 消息流容器，两侧留白 -->
    <view wx:else class="msg-list">
      <view class="canteen-card" wx:for="{{canteenList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="canteen-avatar large-avatar">
          <image class="avatar-img" src="{{item.img || '/images/shitang.png'}}" mode="aspectFill" catchtap="previewAvatar" data-img="{{item.img}}"></image>
        </view>
        <view class="card-info adjust-info">
          <view class="card-header-row">
            <text class="canteen-name">{{item.name}}</text>
            <view class="total-rating-card">
              <image src="/images/xingxingx.png" class="star-icon"></image>
              <text>{{item.formatted_rating || '0.0'}}</text>
            </view>
          </view>
          <view class="canteen-second-row">
            <view class="hot-comment" wx:if="{{item.hot_comment}}">"{{item.hot_comment}}"</view>
            <view class="hot-comment" wx:else>暂无评论</view>
            <text class="score-people-text">{{item.total_comment_count || 0}}人已评分</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 抽取今日美食按钮 - 漫画风格 -->
    <view wx:if="{{canteenList.length > 0}}" class="extract-food-btn" bindtap="showExtractModal">
      <view class="btn-inner">
        <text class="btn-text">抽取今日美食</text>
      </view>
    </view>

    <!-- 感谢文字 -->
    <view wx:if="{{canteenList.length > 0}}" class="credit-text">
      <text>感谢hyb同学对此功能的创意贡献</text>
    </view>

    <!-- 添加按钮 - 仅管理员可见 -->
    <fixed-add-btn wx:if="{{canManageCanteen}}" bind:add="goToAddPage" />
  </view>

  <!-- 抽取今日美食弹窗 -->
  <view wx:if="{{showModal}}" class="modal-overlay">
    <view class="modal-content" catchtap="stopPropagation">
      <view class="modal-header">
        <text class="modal-title">抽取今日美食</text>
        <image src="/images/guanbi.png" class="close-btn" bindtap="hideModal"></image>
      </view>

      <view class="modal-body">
        <!-- 选择食堂 -->
        <view class="section">
          <view class="section-title">选择食堂</view>
          <view class="dropdown-container">
            <view class="dropdown-selected" bindtap="toggleDropdown">
              <text>{{selectedCanteenName}}</text>
              <image src="/images/xiangxiajiantou.png" class="dropdown-arrow {{showDropdown ? 'rotate' : ''}}"></image>
            </view>
            <view wx:if="{{showDropdown}}" class="dropdown-list">
              <view class="dropdown-item {{selectedCanteenIds.join(',') === '1,2' ? 'selected' : ''}}"
                    bindtap="selectCanteen" data-ids="1,2" data-name="沙河校区">
                沙河校区
              </view>
              <view class="dropdown-item {{selectedCanteenIds.join(',') === '3,4,5' ? 'selected' : ''}}"
                    bindtap="selectCanteen" data-ids="3,4,5" data-name="学院路校区">
                学院路校区
              </view>
              <view wx:for="{{canteenList}}" wx:key="id"
                    class="dropdown-item {{selectedCanteenIds.length === 1 && selectedCanteenIds[0] === item.id ? 'selected' : ''}}"
                    bindtap="selectCanteen" data-ids="{{item.id}}" data-name="{{item.name}}">
                {{item.name}}
              </view>
            </view>
          </view>
        </view>

        <!-- 评分限制 -->
        <view class="section">
          <view class="section-title">评分限制</view>
          <view class="rating-input">
            <text>高于</text>
            <view class="dropdown-container rating-dropdown">
              <view class="dropdown-selected" bindtap="toggleRatingDropdown">
                <text>{{minRatingText}}</text>
                <image src="/images/xiangxiajiantou.png" class="dropdown-arrow {{showRatingDropdown ? 'rotate' : ''}}"></image>
              </view>
              <view wx:if="{{showRatingDropdown}}" class="dropdown-list rating-list">
                <view wx:for="{{ratingOptions}}" wx:key="value"
                      class="dropdown-item {{minRating === item.value ? 'selected' : ''}}"
                      bindtap="selectRating" data-value="{{item.value}}" data-text="{{item.text}}">
                  {{item.text}}
                </view>
              </view>
            </view>
            <text>的窗口</text>
          </view>
        </view>

        <!-- 抽取按钮 -->
        <view class="extract-btn {{isExtracting ? 'extracting' : ''}}" bindtap="extractFood">
          <text wx:if="{{!isExtracting}}">开始抽取</text>
          <text wx:else>抽取中...</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 抽取结果弹窗 -->
  <view wx:if="{{showResult}}" class="result-overlay">
    <view class="result-content" catchtap="stopPropagation">
      <view class="result-header">
        <text class="result-title">你的今日想吃</text>
        <image src="/images/guanbi.png" class="close-btn" bindtap="hideResult"></image>
      </view>

      <view class="result-body">
        <view class="result-window {{resultAnimation ? 'animate' : ''}}">
          <view class="window-avatar">
            <image src="{{resultWindow.avatar}}" class="avatar-img" mode="aspectFill"></image>
          </view>
          <view class="window-name">{{resultWindow.name}}</view>
          <view class="window-location">
            <text class="result-canteen-name">{{resultWindow.canteen_name}}</text>
            <text class="result-floor-info">{{resultWindow.floor}}</text>
          </view>
          <view class="window-rating">
            <image src="/images/xingxingx.png" class="star-icon"></image>
            <text>{{resultWindow.rating}}</text>
          </view>
        </view>

        <view class="result-actions">
          <view class="action-btn secondary" bindtap="extractAgain">
            <text>再抽一次</text>
          </view>
          <view class="action-btn primary" bindtap="goToWindow">
            <text>去看看</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>
