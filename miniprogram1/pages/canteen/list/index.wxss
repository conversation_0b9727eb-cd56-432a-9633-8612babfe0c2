.canteen-bg {
  min-height: 100vh;
  background: #f5f6fa;
  padding: 24rpx 0;
}

.content {
  padding: 0;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0 80rpx 0;
}

.empty-icon {
  width: 220rpx;
  height: 220rpx;
  margin-bottom: 32rpx;
  opacity: 0.85;
}

.empty-text {
  color: #7a8fa6;
  font-size: 30rpx;
  margin-bottom: 40rpx;
  font-weight: 500;
}

.empty-btn {
  background: linear-gradient(90deg, #5ec6fa 0%, #3487ef 100%);
  color: #fff;
  padding: 20rpx 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(52,135,239,0.13);
  transition: all 0.3s;
  letter-spacing: 2rpx;
}

.empty-btn:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 12rpx rgba(52,135,239,0.10);
}

/* 食堂列表样式 */
.canteen-list {
  display: flex;
  flex-direction: column;
  gap: 36rpx;
  align-items: center;
  margin-top: 24rpx;
}

.canteen-card {
  width: 80vw;
  margin: 0 auto 24rpx auto;
  padding: 24rpx 28rpx;
  background: #fff;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 24rpx;
  transition: all 0.3s ease;
  min-height: 140rpx;
  align-items: center;
}

.canteen-card:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.02);
}

.canteen-avatar {
  width: 140rpx;
  height: 140rpx;
  border-radius: 18rpx;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  align-self: center;
}

.avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.card-info {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.card-header-row {
  display: flex;
  flex-direction: row;
  align-items: flex-start;
  justify-content: space-between;
  margin-bottom: 4rpx;
  position: relative;
  z-index: 2;
  margin-top: -6rpx;
}

.canteen-name {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.total-rating-card {
  display: flex;
  align-items: center;
  background: #fff7e6;
  color: #ff9500;
  font-size: 26rpx;
  font-weight: 600;
  border-radius: 16rpx;
  padding: 0 18rpx;
  height: 48rpx;
  min-width: 80rpx;
  box-sizing: border-box;
  position: absolute;
  right: 0;
  top: 0;
}

.star-icon {
  width: 24rpx;
  height: 24rpx;
  margin-right: 4rpx;
}

.canteen-second-row {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
  margin-top: 8rpx;
}

.hot-comment {
  font-size: 26rpx;
  color: #5ec6fa;
  font-weight: 600;
  line-height: 1.4;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.score-people-text {
  font-size: 22rpx;
  color: #999;
  margin-top: 4rpx;
}

.msg-list {
  width: 100vw;
  box-sizing: border-box;
  display: flex;
  flex-direction: column;
  gap: 0;
  align-items: center;
  margin-top: 0;
}

/* 抽取今日美食按钮 - 漫画风格 */
.extract-food-btn {
  position: relative;
  width: 60vw;
  margin: 40rpx auto 60rpx auto;
  padding: 0;
  background: none;
  border: none;
  transform: rotate(-0.5deg);
  transition: transform 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.extract-food-btn::before {
  content: "";
  position: absolute;
  top: 8rpx;
  left: 8rpx;
  right: -8rpx;
  bottom: -8rpx;
  background-color: rgba(0, 0, 0, 0.75);
  z-index: 0;
  border-radius: 16rpx;
}

.extract-food-btn::after {
  content: "";
  position: absolute;
  top: -6rpx;
  left: -6rpx;
  right: -6rpx;
  bottom: -6rpx;
  background-color: #ffef00;
  border: 3rpx solid #000000;
  z-index: 1;
  border-radius: 16rpx;
}

.extract-food-btn .btn-inner {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #ff3d3d;
  color: #000000;
  padding: 20rpx 32rpx;
  z-index: 3;
  overflow: hidden;
  transform: skew(-1deg, 0.3deg);
  border-radius: 12rpx;
  clip-path: polygon(
    0% 10%,
    3% 0%,
    97% 0%,
    100% 10%,
    100% 90%,
    97% 100%,
    3% 100%,
    0% 90%
  );
}

.extract-food-btn .btn-inner::before {
  content: "";
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: radial-gradient(
    circle at 30% 30%,
    rgba(0, 0, 0, 0.2) 2rpx,
    transparent 2rpx
  );
  background-size: 10rpx 10rpx;
  background-position: 0 0;
  opacity: 0.3;
  z-index: 2;
}

.extract-food-btn .btn-text {
  position: relative;
  font-weight: 900;
  font-size: 36rpx;
  letter-spacing: 2rpx;
  text-transform: uppercase;
  z-index: 5;
  color: #000000;
  text-shadow:
    1rpx 1rpx 0 #ffffff,
    -1rpx -1rpx 0 #ffffff,
    1rpx -1rpx 0 #ffffff,
    -1rpx 1rpx 0 #ffffff;
  transform: rotate(0.5deg);
}



.extract-food-btn:active {
  transform: rotate(0) scale(0.98);
}

.extract-food-btn:active .btn-inner {
  transform: skew(0, 0);
  background-color: #3d3dff;
}

.extract-food-btn:active .btn-text {
  color: #ffffff;
  text-shadow:
    1rpx 1rpx 0 #000000,
    -1rpx -1rpx 0 #000000,
    1rpx -1rpx 0 #000000,
    -1rpx 1rpx 0 #000000;
  animation: burst 0.3s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes burst {
  0% {
    transform: scale(0.95) rotate(2deg);
  }
  50% {
    transform: scale(1.05) rotate(2deg);
  }
  100% {
    transform: scale(1) rotate(2deg);
  }
}

/* 感谢文字样式 */
.credit-text {
  text-align: center;
  margin: 16rpx auto 40rpx auto;
  width: 60vw;
}

.credit-text text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.4;
}

/* 弹窗遮罩 */
.modal-overlay, .result-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0,0,0,0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 弹窗内容 */
.modal-content {
  width: 85vw;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: visible; /* 改为visible以显示下拉菜单 */
  animation: modalSlideIn 0.3s ease-out;
  position: relative;
}

.result-content {
  width: 85vw;
  max-width: 600rpx;
  background: #fff;
  border-radius: 24rpx;
  overflow: hidden;
  animation: modalSlideIn 0.3s ease-out;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: translateY(-50rpx) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* 弹窗头部 */
.modal-header, .result-header {
  padding: 32rpx 40rpx 24rpx 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.modal-title, .result-title {
  font-size: 36rpx;
  font-weight: 900;
  color: #000000;
  text-shadow:
    2rpx 2rpx 0 #ffffff,
    -2rpx -2rpx 0 #ffffff,
    2rpx -2rpx 0 #ffffff,
    -2rpx 2rpx 0 #ffffff;
  letter-spacing: 2rpx;
  transform: rotate(-1deg);
}

.close-btn {
  width: 40rpx;
  height: 40rpx;
  opacity: 0.6;
}

/* 弹窗主体 */
.modal-body, .result-body {
  padding: 40rpx;
  overflow: visible; /* 确保下拉菜单可见 */
}

/* 选项区域 */
.section {
  margin-bottom: 40rpx;
}

.section-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 24rpx;
}

/* 下拉选择 */
.dropdown-container {
  position: relative;
}

.dropdown-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 24rpx 28rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #ffffff 100%);
  border: 3rpx solid #e3f2fd;
  border-radius: 20rpx;
  font-size: 28rpx;
  color: #333;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(0,0,0,0.08);
  transition: all 0.3s ease;
}

.dropdown-selected:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.12);
}

.dropdown-arrow {
  width: 24rpx;
  height: 24rpx;
  transition: transform 0.3s ease;
}

.dropdown-arrow.rotate {
  transform: rotate(180deg);
}

.dropdown-list {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: #fff;
  border: 3rpx solid #e3f2fd;
  border-radius: 20rpx;
  box-shadow: 0 12rpx 32rpx rgba(0,0,0,0.15);
  z-index: 1000;
  max-height: 400rpx;
  overflow-y: auto;
  margin-top: 12rpx;
  animation: dropdownSlideIn 0.3s ease-out;
}

@keyframes dropdownSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10rpx) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.dropdown-item {
  padding: 24rpx 28rpx;
  font-size: 28rpx;
  color: #333;
  border-bottom: 1rpx solid #f5f5f5;
  transition: all 0.3s ease;
  position: relative;
}

.dropdown-item:last-child {
  border-bottom: none;
}

.dropdown-item:active {
  background: #f0f8ff;
  transform: scale(0.98);
}

.dropdown-item.selected {
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  color: #1976d2;
  font-weight: 700;
  border-left: 6rpx solid #2196f3;
}

.dropdown-item.selected::after {
  content: "✓";
  position: absolute;
  right: 28rpx;
  top: 50%;
  transform: translateY(-50%);
  color: #2196f3;
  font-weight: 900;
  font-size: 32rpx;
}

/* 评分输入 */
.rating-input {
  display: flex;
  align-items: center;
  gap: 16rpx;
  font-size: 28rpx;
  color: #666;
  font-weight: 600;
}

.rating-dropdown {
  min-width: 120rpx;
}

.rating-dropdown .dropdown-selected {
  padding: 12rpx 20rpx;
  min-width: 100rpx;
  justify-content: center;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  border-color: #ffb74d;
  color: #e65100;
  font-size: 26rpx;
  border-radius: 16rpx;
}

.rating-list {
  max-height: 300rpx;
  min-width: 120rpx;
}

.rating-list .dropdown-item {
  text-align: center;
  padding: 20rpx 24rpx;
}

.rating-list .dropdown-item.selected {
  background: linear-gradient(135deg, #fff3e0 0%, #ffcc02 100%);
  color: #e65100;
  border-left: 6rpx solid #ff9800;
}

.rating-list .dropdown-item.selected::after {
  display: none; /* 隐藏评分选项的对勾 */
}

/* 抽取按钮 - 漫画风格 */
.extract-btn {
  position: relative;
  width: 100%;
  padding: 0;
  background: none;
  border: none;
  margin-top: 20rpx;
  transform: rotate(-0.3deg);
  transition: transform 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.extract-btn::before {
  content: "";
  position: absolute;
  top: 6rpx;
  left: 6rpx;
  right: -6rpx;
  bottom: -6rpx;
  background-color: rgba(0, 0, 0, 0.6);
  z-index: 0;
  border-radius: 12rpx;
}

.extract-btn::after {
  content: "";
  position: absolute;
  top: -4rpx;
  left: -4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background-color: #ffef00;
  border: 2rpx solid #000000;
  z-index: 1;
  border-radius: 12rpx;
}

.extract-btn text {
  position: relative;
  display: block;
  background-color: #ffef00;
  color: #000000;
  padding: 28rpx 0;
  z-index: 3;
  text-align: center;
  font-size: 32rpx;
  font-weight: 900;
  letter-spacing: 1rpx;
  border-radius: 8rpx;
  transform: skew(-2deg, 0.5deg);
  text-shadow:
    1rpx 1rpx 0 #ffffff,
    -1rpx -1rpx 0 #ffffff,
    1rpx -1rpx 0 #ffffff,
    -1rpx 1rpx 0 #ffffff;
}

.extract-btn:active {
  transform: rotate(0) scale(0.98);
}

.extract-btn:active text {
  transform: skew(0, 0);
  background-color: #2196f3;
  color: #ffffff;
  text-shadow:
    1rpx 1rpx 0 #000000,
    -1rpx -1rpx 0 #000000,
    1rpx -1rpx 0 #000000,
    -1rpx 1rpx 0 #000000;
}

.extract-btn.extracting text {
  background-color: #999;
  color: #666;
  text-shadow: none;
}

/* 结果展示 */
.result-window {
  text-align: center;
  padding: 40rpx 0;
  transition: all 0.5s ease;
}

.result-window.animate {
  animation: resultShow 0.8s ease-out;
}

@keyframes resultShow {
  0% {
    opacity: 0;
    transform: scale(0.5) rotate(-10deg);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.1) rotate(5deg);
  }
  100% {
    opacity: 1;
    transform: scale(1) rotate(0deg);
  }
}

.window-avatar {
  position: relative;
  width: 160rpx;
  height: 160rpx;
  margin: 0 auto 24rpx auto;
  border-radius: 24rpx;
  overflow: hidden;
  transform: rotate(-0.5deg);
  border: 6rpx solid #000000;
  box-shadow:
    0 8rpx 24rpx rgba(0,0,0,0.3),
    inset 0 0 0 4rpx #ffef00;
}

.window-avatar::before {
  content: "";
  position: absolute;
  top: -8rpx;
  left: -8rpx;
  right: -8rpx;
  bottom: -8rpx;
  background: linear-gradient(45deg, #ff3d3d, #3d3dff, #4caf50, #ff9500);
  border-radius: 32rpx;
  z-index: -1;
  animation: avatarGlow 2s ease-in-out infinite alternate;
}

@keyframes avatarGlow {
  0% {
    opacity: 0.6;
    transform: scale(1);
  }
  100% {
    opacity: 0.9;
    transform: scale(1.05);
  }
}

.window-avatar .avatar-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.window-name {
  font-size: 40rpx;
  font-weight: 900;
  color: #000000;
  margin-bottom: 12rpx;
  text-shadow:
    2rpx 2rpx 0 #ffffff,
    -2rpx -2rpx 0 #ffffff,
    2rpx -2rpx 0 #ffffff,
    -2rpx 2rpx 0 #ffffff;
  letter-spacing: 2rpx;
  transform: rotate(-0.3deg);
}

.window-location {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 16rpx;
  margin-bottom: 16rpx;
}

.result-canteen-name {
  font-size: 26rpx;
  color: #5ec6fa;
  font-weight: 600;
  background: linear-gradient(135deg, #e3f2fd 0%, #bbdefb 100%);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid #2196f3;
}

.result-floor-info {
  font-size: 26rpx;
  color: #ff9500;
  font-weight: 600;
  background: linear-gradient(135deg, #fff3e0 0%, #ffe0b2 100%);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  border: 2rpx solid #ff9800;
}

.window-rating {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #ff9500;
  font-weight: 600;
}

.window-rating .star-icon {
  width: 32rpx;
  height: 32rpx;
}

/* 操作按钮 - 漫画风格 */
.result-actions {
  display: flex;
  gap: 24rpx;
  margin-top: 40rpx;
}

.action-btn {
  position: relative;
  flex: 1;
  padding: 0;
  background: none;
  border: none;
  transform: rotate(-0.3deg);
  transition: transform 0.3s cubic-bezier(0.22, 0.61, 0.36, 1);
}

.action-btn::before {
  content: "";
  position: absolute;
  top: 4rpx;
  left: 4rpx;
  right: -4rpx;
  bottom: -4rpx;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 0;
  border-radius: 10rpx;
}

.action-btn::after {
  content: "";
  position: absolute;
  top: -3rpx;
  left: -3rpx;
  right: -3rpx;
  bottom: -3rpx;
  background-color: #ffef00;
  border: 2rpx solid #000000;
  z-index: 1;
  border-radius: 10rpx;
}

.action-btn text {
  position: relative;
  display: block;
  padding: 24rpx 0;
  z-index: 3;
  text-align: center;
  font-size: 30rpx;
  font-weight: 900;
  letter-spacing: 1rpx;
  border-radius: 6rpx;
  transform: skew(-1deg, 0.5deg);
}

.action-btn.secondary text {
  background-color: #f5f5f5;
  color: #000000;
  text-shadow:
    1rpx 1rpx 0 #ffffff,
    -1rpx -1rpx 0 #ffffff,
    1rpx -1rpx 0 #ffffff,
    -1rpx 1rpx 0 #ffffff;
}

.action-btn.primary text {
  background-color: #2196f3;
  color: #ffffff;
  text-shadow:
    1rpx 1rpx 0 #000000,
    -1rpx -1rpx 0 #000000,
    1rpx -1rpx 0 #000000,
    -1rpx 1rpx 0 #000000;
}

.action-btn:active {
  transform: rotate(0) scale(0.98);
}

.action-btn:active text {
  transform: skew(0, 0);
}

.action-btn.secondary:active text {
  background-color: #e0e0e0;
}

.action-btn.primary:active text {
  background-color: #1976d2;
}
