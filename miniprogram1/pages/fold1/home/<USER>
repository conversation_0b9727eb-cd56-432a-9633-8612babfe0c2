/* 顶部标签样式 */
.grid-list{
  display: flex;
  flex-wrap: wrap;
  background-color: white;
  border-radius: 25rpx;
  position: fixed;
  margin-top: 0rpx;
  left: 27rpx;
  width: 700rpx;
  height: 135rpx; /* 设置占位高度 */
  z-index: 999;  /* 添加较高的z-index */
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);  /* 添加阴影效果增强层次感 */
 /* 距离屏幕左侧的距离 */
}
.grid-item{
  width: 20%;
  height: 150rpx;
  display: flex;
  flex-direction:column;
  align-items: center;
  justify-content: center;
  box-sizing:border-box;
}
.search-container {
  display: flex;
  align-items: center;
  height: 100%;
  z-index: 999;
  position: relative;
}
.search-input {
  width: 270rpx;
  height: 58rpx;
  line-height: 58rpx;
  margin-bottom: 10rpx;
  margin-right: 183rpx;
  border: none;
  outline: none;
  background: white url('https://www.bjgaoxiaoshequ.store/images/放大镜.png') no-repeat;
  background-size: 40rpx 40rpx;
  background-position: 10rpx center;
  color: grey;
  border-radius: 40rpx;
  text-align: left;
  padding: 0 5rpx;
  padding-left: 55rpx;
}
.grid-item image{
  width: 40rpx;
  height: 40rpx;

}
.grid-item text{
  font-size: 25rpx;
  margin-top: 10rpx;
  font-family:Georgia, 'Times New Roman', Times, serif
}
/* 背景颜色 */
.gradient-background {
  min-height: 100vh;
  height: auto;
  background: linear-gradient(to bottom, rgb(245, 239, 227),white);
  width: 100%;
  overflow-x: hidden;
  position: relative;
}
/* 文字框样式 */
.num-item, .num-item2 {
  box-sizing: border-box;
  border-radius: 34rpx;
  margin: 35rpx;
  padding: 20rpx 25rpx 15rpx 25rpx;
  display: flex;
  flex-direction: column;
  width: 90%;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  min-height: auto;
  height: auto !important;
  position: relative;
  z-index: 1;
}

/* 匿名帖子样式 */
.num-item.anonymous-item {
  background: rgba(240, 235, 225, 0.9) !important;
  border-left: 4rpx solid rgba(180, 160, 140, 0.8);
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.08);
}

.text {
  font-size: 32rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  word-break: break-word;
  white-space: pre-wrap;
  padding: 0;
}

/* 导航栏 */
.navsetting{
  --nav-bar-background-color: rgb(245, 239, 227);
  z-index: 9999;  /* 添加最高层级的z-index */
  position: relative;  /* 确保z-index生效 */
}

.navtext{
  margin-bottom: 10rpx;
  font-family: '北航-刀隶体 Regular';
  font-size: 62rpx;
  margin-left: 15rpx;
}

.custom-icon2{
  width: 60rpx;
 height: 60rpx;
 margin-right: 10rpx;
}
/* 右侧按钮组 */
.fixed-button {
  position: fixed;
  bottom: 20rpx;
  right: 20rpx;
  color: white;
  background-color: rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999; /* 添加z-index确保按钮显示在最上层 */
}
.custom-icon{
  border-radius: 50%;
  width: 60rpx;
  height: 60rpx;
  display: block;
}
.fixed-button2 {
  position: fixed;
  bottom: 125rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
}
.fixed-button3 {
  position: fixed;
  bottom: 230rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
}
.fixed-button4 {
  position: fixed;
  bottom: 335rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
}
.fixed-button4.active {
  transform: scale(1.2);
  background-color: rgba(255, 107, 107, 0.8);
  animation: pulse 1s infinite;
}

.fixed-button5 {
  position: fixed;
  bottom: 440rpx;
  right: 20rpx;
  color: white;
  background-color:rgba(255, 255, 255,0.5);
  padding: 10rpx;
  border-radius: 50%;
  width: 60rpx; 
  height: 60rpx;
  z-index: 999;
  transition: all 0.3s ease;
  pointer-events: auto;
}
.fixed-button5.active {
  transform: scale(1.2);
  background-color: rgba(255, 107, 107, 0.8);
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10rpx rgba(255, 107, 107, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(255, 107, 107, 0);
  }
}
/* 搜索框设置 */
.srch{
  margin-right:150rpx;
  margin-bottom: 16rpx;
  width: 400rpx;
}
/* 占位样式 */
.zhanwei{
  text-align: center;
  height: 120rpx;
}
.touxiang1 {
  margin-bottom: 15rpx;
  height: 70rpx;
  display: flex;
  background-color: transparent;
  border: 3rpx;
  align-items: flex-start;
}

/* 热榜中的touxiang1样式覆盖 */
.hot-topic-item .touxiang1 {
  margin-bottom: 0;
}

.image-container {
  margin-top: 8rpx;
  display: flex;
  flex-direction: row;
  justify-content: flex-start;
}

.image-item {
  margin-left: 0rpx;
  flex: 0 0 auto;
  margin-right: 18rpx;
}

.uniform-image {
  width: 198rpx;
  height: 198rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.kuang {
  height: 60rpx;
  margin-top: auto;
  display: flex;
  align-items: center;
}

.last {
  font-size: 25rpx;
  display: flex;
  align-items: center;
  margin-left: auto;
  height: 35rpx;
  line-height: 35rpx;
}

.action-item {
  display: flex;
  align-items: center;
  margin-left: 10rpx;
  position: relative;
  z-index: 2;
}

.action-item image {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
}

.action-item view {
  margin-left: 10rpx;
  color: #000000;
  font-size: 25rpx;
}

.gradient-text {
  font-size: 24rpx;
  color: #808080;
  line-height: 35rpx;
}

.last image {
  width: 35rpx;
  height: 35rpx;
  margin-left: 10rpx;
  vertical-align: middle;
}

.last view {
  margin-left: 10rpx;
  line-height: 35rpx;
}

.touxian{
  padding: 7rpx;
  margin-left:15rpx;
  background-image: linear-gradient(to left, #f64f59, #c471ed, #12c2e9);
  /* background-image: linear-gradient(to left, #FF0080, #FF8C00, #40E0D0); */
  color: white; 
  font-size: 20rpx;
  border-radius: 8rpx;
  line-height: 1
}
.touxian2{
  padding: 7rpx;
  margin-left:15rpx;
  /* background-image: linear-gradient(to left, #f64f59, #c471ed, #12c2e9); */
  /* background-image: linear-gradient(to left, #FF0080, #FF8C00, #40E0D0); */
  color: white; 
  font-size: 20rpx;
  border-radius: 8rpx;
  line-height: 1
}

/* 投票样式 */
.vote-container {
  margin: 0;
  padding: 0;
  background-color: transparent;
  position: relative;
}

.vote-header {
  margin-bottom: 0;
}

.vote-title-row {
  display: flex;
  align-items: center;
  gap: 10rpx;
}

.vote-title-icon {
  width: 28rpx;
  height: 28rpx;
}

.vote-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
}

.vote-type-tag {
  font-size: 22rpx;
  color: #fff;
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  padding: 4rpx 12rpx;
  border-radius: 24rpx;
  display: inline-flex;
  align-items: center;
  height: fit-content;
  line-height: 1.4;
  box-shadow: 0 2rpx 6rpx rgba(255, 165, 0, 0.2);
}

.vote-options {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.vote-option-box {
  border: 1px solid rgba(255, 165, 0, 0.3);
  border-radius: 28rpx;
  padding: 16rpx 20rpx;
  position: relative;
  overflow: hidden;
  background-color: transparent;
  min-height: 60rpx;
  box-sizing: border-box;
}

.vote-option-box.selected {
  border-color: rgba(255, 165, 0, 0.8);
}

.vote-progress-bg {
  position: absolute;
  left: 0;
  top: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  border-radius: 28rpx;
  overflow: hidden;
  z-index: 0;
}

.vote-progress-fill {
  position: absolute;
  left: 0;
  top: 0;
  height: 100%;
  width: 0;
  background: rgba(255, 165, 0, 0.15);
  transition: width 0.6s ease-in-out;
}

.vote-option-box.selected .vote-progress-fill {
  background: rgba(255, 165, 0, 0.35);
  width: 100%;
  transition: none;
}

.vote-option-box.voted {
  border-color: rgba(255, 165, 0, 0.8);
}

.vote-option-box.voted .vote-progress-fill {
  background: rgba(255, 165, 0, 0.35);
}

.vote-option-content {
  position: relative;
  z-index: 1;
  display: flex;
  justify-content: space-between;
  align-items: center;
  min-height: 36rpx;
}

.vote-option-text {
  font-size: 26rpx;
  color: #333;
  margin-right: 12rpx;
  line-height: 1.5;
  flex: 1;
}

.vote-count-text {
  font-size: 22rpx;
  color: #666;
  flex-shrink: 0;
  min-width: 40rpx;
  text-align: right;
  line-height: 1.5;
}

.vote-button-area {
  margin-top: 30rpx;
  display: flex;
  justify-content: center;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
  pointer-events: none;
  visibility: hidden;
}

.vote-button-area.show-multi {
  opacity: 1;
  transform: translateY(0);
  pointer-events: auto;
  visibility: visible;
}

.vote-button-area.show-single {
  display: none;
}

.vote-submit-button {
  background-color: #ddd;
  color: #888;
  font-size: 26rpx;
  padding: 12rpx 50rpx;
  border-radius: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.vote-submit-button.active {
  background: linear-gradient(135deg, #ffd700 0%, #ffa500 100%);
  color: white;
  box-shadow: 0 4rpx 12rpx rgba(255, 165, 0, 0.3);
}

.vote-submit-button.active:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 8rpx rgba(255, 165, 0, 0.2);
}

.divider {
  height: 2rpx;
  background-color: #f0f0f0;
  margin: 10rpx 0;
  margin-top: 20rpx;
}

/* 更新公告动画相关样式 */
.update-notice-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: visible;
  pointer-events: none;
}

.update-mask-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9998;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.update-show .update-mask-background {
  opacity: 1;
  pointer-events: auto;
}

.update-hide .update-mask-background {
  opacity: 0;
  pointer-events: none;
}

.update-notice-box {
  width: 600rpx;
  background: linear-gradient(135deg, #FFFFFF, #FFF5F8);
  padding: 40rpx 0;
  border-radius: 30rpx;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.1);
  position: relative;
  z-index: 10000;
  opacity: 0;
  transform: scale(0.1);
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  pointer-events: none;
  max-height: 85vh;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform-origin: center;
}

.update-show .update-notice-box {
  opacity: 1;
  transform: scale(1);
  pointer-events: auto;
}

.update-hide .update-notice-box {
  opacity: 0;
  transform: scale(0.1);
  pointer-events: none;
}

/* 更新公告内容样式 */
.update-notice-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
  padding-bottom: 15rpx;
  margin-bottom: 15rpx;
  border-bottom: 2rpx solid rgba(255, 105, 180, 0.1);
  width: calc(100% - 50rpx);
  margin-left: auto;
  margin-right: auto;
}

.update-notice-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 15rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(255, 105, 180, 0.2);
}

.update-notice-body {
  flex: 0 0 auto;
  margin: 20rpx 0;
  padding: 25rpx;
  position: relative;
  background: rgba(255, 255, 255, 0.6);
  border-radius: 20rpx;
  border: 2rpx solid rgba(255, 105, 180, 0.1);
  width: calc(100% - 50rpx);
  box-sizing: border-box;
  margin-left: auto;
  margin-right: auto;
}

.update-notice-box .update-text {
  white-space: pre-line;
  line-height: 1.8;
  color: #333;
  font-size: 28rpx;
  display: block;
  padding: 0;
  width: 100%;
  word-break: break-word;
  text-align: left;
  margin: 0;
}

.update-notice-box .update-title {
  font-size: 32rpx;
  font-weight: 600;
  background: linear-gradient(135deg, #FF69B4, #FF8C98);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 8rpx;
  text-align: center;
}

.update-notice-box .update-version {
  font-size: 24rpx;
  color: #FF69B4;
  margin-bottom: 6rpx;
  text-align: center;
  opacity: 0.9;
}

.update-notice-box .update-date {
  font-size: 22rpx;
  color: #999;
  text-align: center;
  margin-bottom: 0;
}

.update-notice-footer {
  padding-top: 25rpx;
  margin-top: auto;
  text-align: center;
  border-top: 2rpx solid rgba(255, 105, 180, 0.1);
  width: 100%;
}

.update-btn-group {
  display: flex;
  justify-content: center;
  gap: 20rpx;
  width: 90%;
  margin: 0 auto;
  box-sizing: border-box;
}

.update-btn-later {
  background: #f5f5f5 !important;
  color: #666 !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05) !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  border: none !important;
  font-weight: 500 !important;
  max-width: 220rpx !important;
  text-align: center !important;
}

.update-btn-check {
  background: linear-gradient(135deg, #FF69B4, #FF8C98) !important;
  color: white !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 6rpx 16rpx rgba(255, 105, 180, 0.3) !important;
  border: none !important;
  position: relative;
  overflow: hidden;
  font-weight: 500 !important;
  max-width: 220rpx !important;
  text-align: center !important;
}

.update-btn-check::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shineEffect 3s infinite;
}

.update-btn-later:active {
  transform: scale(0.98);
  background: #f0f0f0 !important;
}

.update-btn-check:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 105, 180, 0.2) !important;
}

@keyframes shineEffect {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

/* 烟花动画样式 */
.fireworks-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 9998;
}

.firework {
  position: absolute;
  bottom: 0;
  width: 3rpx;
  height: 3rpx;
  border-radius: 50%;
  animation: launch 1.2s cubic-bezier(0.25, 0.46, 0.45, 0.94) forwards;
}

.particle {
  position: absolute;
  width: 3rpx;
  height: 15rpx;
  border-radius: 50%;
  transform-origin: center center;
  opacity: 0;
  box-shadow: 0 0 6rpx 1rpx currentColor;
}

.particle-branch {
  position: absolute;
  width: 100%;
  height: 100%;
  transform-origin: center;
}

@keyframes launch {
  0% {
    transform: translateY(0) scale(1);
    opacity: 1;
  }
  50% {
    transform: translateY(-45vh) scale(1);
    opacity: 1;
  }
  55% {
    transform: translateY(-50vh) scale(1.2);
    opacity: 0;
  }
  100% {
    transform: translateY(-50vh) scale(1);
    opacity: 0;
  }
}

@keyframes explode {
  0% {
    transform: rotate(var(--angle)) translateY(0) scale(1);
    opacity: 0;
  }
  5% {
    opacity: 1;
    transform: rotate(var(--angle)) translateY(5rpx) scale(1);
  }
  35% {
    opacity: 0.9;
    transform: rotate(var(--angle)) translateY(var(--distance)) scale(1);
  }
  70% {
    opacity: 0.4;
    transform: rotate(var(--angle)) translateY(calc(var(--distance) * 1.2)) scale(0.8);
  }
  100% {
    opacity: 0;
    transform: rotate(var(--angle)) translateY(calc(var(--distance) * 1.5)) scale(0);
  }
}

@keyframes sparkle {
  0% {
    opacity: 0;
    transform: scale(1) rotate(0deg);
  }
  10% {
    opacity: 1;
  }
  50% {
    opacity: 0.7;
  }
  100% {
    opacity: 0;
    transform: scale(0.5) rotate(180deg);
  }
}

.gradient-text2 {
  background: linear-gradient(to right, #b829db, #213ddd);
  -webkit-background-clip: text;
  color: transparent;
}

/* 引导提示样式 */
.guide-hint-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;
  visibility: visible;
  pointer-events: none;
}

.mask-background {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
  z-index: 9998;
  opacity: 0;
  transition: opacity 0.3s ease-in-out;
  pointer-events: none;
}

.guide-show .mask-background {
  opacity: 1;
  pointer-events: auto;
}

.guide-hide .mask-background {
  opacity: 0;
  pointer-events: none;
}

.guide-hint-content {
  width: 500rpx;
  background: linear-gradient(135deg, #FFFFFF, #F9FAFF);
  border-radius: 20rpx;
  padding: 30rpx;
  box-shadow: 0 8rpx 24rpx rgba(149, 157, 165, 0.1);
  position: relative;
  z-index: 10000;
  transition: all 0.8s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  transform-origin: center;
  opacity: 1;
  overflow: hidden;
}

.guide-show {
  pointer-events: auto;
}

.guide-hide {
  pointer-events: none;
}

.guide-hint-header {
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.guide-hint-icon {
  width: 80rpx;
  height: 80rpx;
  margin-bottom: 20rpx;
}

.guide-hint-title {
  font-size: 40rpx;
  font-weight: bold;
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  -webkit-background-clip: text;
  color: transparent;
  margin-bottom: 10rpx;
}

.guide-hint-body {
  padding: 20rpx 0;
}

.guide-text {
  display: block;
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  text-align: left;
  padding: 10rpx;
}

.guide-hint-footer {
  margin-top: 20rpx;
  text-align: center;
}

.guide-hint-btn {
  background: linear-gradient(135deg, #FF69B4, #FFB6C1);
  color: white;
  border: none;
  padding: 12rpx 40rpx;
  border-radius: 30rpx;
  font-size: 26rpx;
  box-shadow: 0 4rpx 12rpx rgba(255, 182, 193, 0.3);
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  min-width: 180rpx;
  max-width: 300rpx;
  margin: 0 auto;
}

.guide-hint-btn.gradient-btn {
  background: linear-gradient(135deg, #FFB6C1, #FFD700);
}

/* 修改高亮区域样式 */
.highlight-nav {
  position: fixed;
  bottom: 230rpx;
  right: 120rpx;
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.highlight-content {
  position: fixed;
  bottom: 335rpx;
  right: 120rpx;
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.highlight-publish {
  position: fixed;
  bottom: 125rpx;
  right: 120rpx;
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10000;
}

.highlight-message {
  position: fixed;
  top: 223rpx;
  left: 0;
  width: 100%;
  bottom: 0;
  background: transparent;  /* 移除背景色，因为已经由mask-background提供 */
  z-index: 10000;
}

.highlight-message .guide-hint-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
}

.highlight-message .guide-hint-footer {
  position: fixed;
  top: calc(50% + 80rpx);
  left: 50%;
  transform: translateX(-50%);
  width: 500rpx;
  text-align: center;
}

/* 移除之前的before伪元素 */
.highlight-message::before {
  display: none;
}

/* 按钮脉冲动画 */
.pulse {
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

/* 引导提示动画 */
.guide-show .guide-hint-content {
  opacity: 1;
  transform: scale(1);
  animation: slideIn 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.guide-hide .guide-hint-content {
  opacity: 0;
  transform: scale(0.8);
  animation: slideOut 0.6s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

@keyframes slideIn {
  0% {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
  }
  100% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes slideOut {
  0% {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
  100% {
    opacity: 0;
    transform: scale(0.8) translateY(60rpx);
  }
}

@keyframes bounce {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-20rpx);
  }
  60% {
    transform: translateY(-10rpx);
  }
}

/* 引导激活状态 */
.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

/* 第五步时的特殊处理 */
.guide-hint-mask.step-5 {
  pointer-events: none;
}

.guide-hint-mask.step-5 .mask-background {
  background: rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

/* 第五步的高亮区域 */
.highlight-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 10000;
  pointer-events: auto;
}

/* 第五步的提示文本容器 */
.highlight-message .guide-hint-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

.highlight-message .guide-hint-footer {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 80rpx);
  width: 500rpx;
  text-align: center;
  z-index: 10001;
}

/* 第五步跳过按钮位置调整 */
.highlight-message .skip-guide {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  transform: none;
}

/* 保持搜索框和标签栏可点击 */
.guide-hint-mask.step-5 .search-container.guide-active,
.guide-hint-mask.step-5 .grid-list.guide-active {
  pointer-events: auto;
}

.guide-hint-mask.step-5 .search-container.guide-active .search-input {
  pointer-events: auto;
}

.guide-hint-mask.step-5 .grid-list.guide-active .grid-item {
  pointer-events: auto;
}

/* 修改第五步遮罩层样式 */
.guide-hint-mask.step-5 .mask-background {
  background: rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

/* 第五步的特殊高亮样式 */
.search-container.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

.search-container.guide-active .search-input {
  background: white url('https://www.bjgaoxiaoshequ.store/images/放大镜.png') no-repeat !important;
  background-size: 40rpx 40rpx !important;
  background-position: 10rpx center !important;
}

.grid-list.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95) !important;
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

/* 引导激活状态 */
.guide-active {
  z-index: 10001 !important;
  background-color: rgba(255, 255, 255, 0.95);
  box-shadow: 0 0 20rpx rgba(255, 182, 193, 0.5);
}

/* 第五步时的特殊处理 */
.guide-hint-mask.step-5 {
  pointer-events: none;
}

.guide-hint-mask.step-5 .mask-background {
  background: rgba(0, 0, 0, 0.4);
  pointer-events: auto;
}

/* 第五步的高亮区域 */
.highlight-message {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: transparent;
  z-index: 10000;
  pointer-events: auto;
}

/* 第五步的提示文本容器 */
.highlight-message .guide-hint-body {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 500rpx;
  background: rgba(255, 255, 255, 0.95);
  border-radius: 16rpx;
  padding: 20rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

.highlight-message .guide-hint-footer {
  position: fixed;
  top: 50%;
  left: 50%;
  transform: translate(-50%, 80rpx);
  width: 500rpx;
  text-align: center;
  z-index: 10001;
}

/* 第五步跳过按钮位置调整 */
.highlight-message .skip-guide {
  position: absolute;
  top: 20rpx;
  right: 20rpx;
  transform: none;
}

/* 保持搜索框和标签栏可点击 */
.guide-hint-mask.step-5 .search-container.guide-active,
.guide-hint-mask.step-5 .grid-list.guide-active {
  pointer-events: auto;
}

.guide-hint-mask.step-5 .search-container.guide-active .search-input {
  pointer-events: auto;
}

.guide-hint-mask.step-5 .grid-list.guide-active .grid-item {
  pointer-events: auto;
}

/* 跳过引导按钮基础样式 */
.skip-guide {
  position: absolute;
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 20rpx;
  border-radius: 30rpx;
  background: rgba(255, 255, 255, 0.8);
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  z-index: 10001;
}

/* 第一步的跳过按钮位置 */
.guide-hint-content .skip-guide {
  top: 20rpx;
  right: 20rpx;
}

/* 第二步到第四步的跳过按钮位置 */
.highlight-publish .skip-guide,
.highlight-nav .skip-guide,
.highlight-content .skip-guide {
  bottom: -60rpx;
  right: 20rpx;
  top: auto;
}

/* 第五步的跳过按钮位置 */
.highlight-message .skip-guide {
  bottom: 40%;
  right: 40rpx;
  top: auto;
}

.notice-body {
  max-height: 400rpx;
  overflow-y: auto;
  padding: 20rpx;
  margin: 10rpx 0;
}

.notice-text {
  white-space: pre-wrap;
  word-break: break-word;
  font-size: 28rpx;
  line-height: 1.6;
  display: block;
}

/* 修改滚动条样式 */
.notice-body::-webkit-scrollbar {
  width: 4rpx;
  background-color: transparent;
}

.notice-body::-webkit-scrollbar-thumb {
  background-color: #ddd;
  border-radius: 4rpx;
}

.update-btn-group {
  display: flex;
  justify-content: space-between;
  gap: 20rpx;
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

.update-btn-later {
  background: #f5f5f5 !important;
  color: #666 !important;
  box-shadow: 0 4rpx 8rpx rgba(0, 0, 0, 0.05) !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  border: none !important;
  font-weight: 500 !important;
  width: 50% !important;
  text-align: center !important;
}

.update-btn-check {
  background: linear-gradient(135deg, #FF69B4, #FF8C98) !important;
  color: white !important;
  flex: 1;
  font-size: 28rpx !important;
  height: 88rpx !important;
  line-height: 88rpx !important;
  border-radius: 44rpx !important;
  transition: all 0.3s ease !important;
  box-shadow: 0 6rpx 16rpx rgba(255, 105, 180, 0.3) !important;
  border: none !important;
  position: relative;
  overflow: hidden;
  font-weight: 500 !important;
  width: 50% !important;
  text-align: center !important;
}

.update-btn-check::after {
  content: '';
  position: absolute;
  top: -50%;
  left: -50%;
  width: 200%;
  height: 200%;
  background: linear-gradient(
    to bottom right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0) 40%,
    rgba(255, 255, 255, 0.4) 50%,
    rgba(255, 255, 255, 0) 60%,
    rgba(255, 255, 255, 0) 100%
  );
  transform: rotate(45deg);
  animation: shineEffect 3s infinite;
}

.update-btn-later:active {
  transform: scale(0.98);
  background: #f0f0f0 !important;
}

.update-btn-check:active {
  transform: scale(0.98);
  box-shadow: 0 4rpx 8rpx rgba(255, 105, 180, 0.2) !important;
}

@keyframes shineEffect {
  0% {
    left: -50%;
  }
  100% {
    left: 150%;
  }
}

/* 每日热帖样式 */
.hot-topic-container {
  width: 100%;
  padding: 0;
  box-sizing: border-box;
}

.hot-topic-item {
  background-color: rgba(254, 251, 229, 0.8);
  border-radius: 34rpx;
  padding: 20rpx 25rpx;
  box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.05);
  margin: 35rpx;
  width: 90%;
  box-sizing: border-box;
  position: relative;
  min-height: 120rpx;
  display: flex;
  flex-direction: column;
}

.hot-topic-header {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  height: 70rpx;
}

.hot-topic-avatar {
  width: 70rpx;
  height: 70rpx;
  border-radius: 15%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: none;
  margin-bottom: 0;
  overflow: hidden;
}

.hot-topic-avatar image,
.hot-topic-avatar > image {
  width: 60rpx !important;
  height: 60rpx !important;
  display: block;
  object-fit: contain;
  margin: auto;
}

.hot-topic-username {
  margin-left: 15rpx;
  font-size: 28rpx;
  color: #333;
}

.hot-topic-content {
  display: flex;
  flex-direction: column;
  margin-top: 10rpx;
  overflow: hidden;
}

.hot-topic-list {
  display: flex;
  flex-direction: column;
  gap: 10rpx;
}

.hot-topic-item-row {
  display: flex;
  align-items: flex-start;
  line-height: 45rpx;
}

.hot-topic-rank {
  font-size: 32rpx;
  color: #ecba16;
  font-weight: bold;
  margin-right: 10rpx;
  flex-shrink: 0;
}

/* 修改热榜序号样式 */
.hot-topic-item-row .hot-topic-rank {
  margin-left: 15rpx;
  margin-right: 15rpx;
  color: #ecba16;
  font-size: 28rpx;
  font-weight: bold;
}

.hot-topic-text {
  font-size: 28rpx;
  line-height: 45rpx;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  flex: 1;
  max-height: 45rpx;
  display: -webkit-box;
  -webkit-line-clamp: 1;
  -webkit-box-orient: vertical;
}

.bg0 {
  background-color: #f07264;
  color: #fff;
  padding: 4rpx 12rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
}

.close-icon {
  width: 24rpx;
  height: 24rpx;
  padding: 8rpx;
}

/* 热帖提示弹窗样式 */
.hot-topic-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 9999;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.hot-topic-modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.4);
}

.modal-content {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 600rpx;
  background: #fff;
  border-radius: 20rpx;
  overflow: hidden;
}

.modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #eee;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
}

.modal-close {
  width: 32rpx;
  height: 32rpx;
  padding: 10rpx;
}

.modal-body {
  padding: 40rpx 30rpx;
}

.modal-text {
  font-size: 28rpx;
  color: #666;
  line-height: 1.6;
}

.modal-footer {
  padding: 20rpx 30rpx 30rpx;
  text-align: center;
}

.modal-btn {
  background: #CED5E9;
  color: #333;
  font-size: 28rpx;
  padding: 16rpx 40rpx;
  border-radius: 40rpx;
  border: none;
  display: inline-block;
}

.hot-topic-close-x {
  font-size: 36rpx;
  color: #ecba16;
  font-weight: bold;
  cursor: pointer;
  margin-left: 20rpx;
  margin-right: 10rpx;
  user-select: none;
}

.hot-topic-view-btn {
  background: #f64f59;
  color: #fff;
  border: none;
  padding: 4rpx 18rpx;
  border-radius: 8rpx;
  font-size: 24rpx;
  margin-left: 15rpx;
  font-weight: bold;
  cursor: pointer;
}

/* 赛博朋克风格匿名标签 - 红黄配色静态版 */
.cyber-badge {
  position: relative;
  height: 28rpx;
  width: 64rpx;
  margin-left: 10rpx;
  display: inline-block;
}

.cyber-btn {
  --primary: #ff184c;
  --shadow-primary: #fded00;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  color: var(--color);
  text-transform: uppercase;
  font-size: 18rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 28rpx;
  text-align: center;
}

.cyber-btn::after, .cyber-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
  z-index: -1;
}

.cyber-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.cyber-btn::after {
  background: var(--primary);
}

.cyber-number {
  background: var(--shadow-primary);
  color: #323232;
  font-size: 10rpx;
  font-weight: 700;
  letter-spacing: 0.1rpx;
  position: absolute;
  width: 20rpx;
  height: 8rpx;
  top: 0;
  left: 78%;
  line-height: 8rpx;
  text-align: center;
}

/* 热帖"点击查看"标签的赛博朋克风格 */
.hot-topic-cyber-badge {
  --primary: #ff184c;
  --shadow-primary: #fded00;
  --color: white;
  --clip: polygon(11% 0, 95% 0, 100% 25%, 90% 90%, 95% 90%, 85% 90%, 85% 100%, 7% 100%, 0 80%);
  --border: 1.5rpx;

  position: relative;
  height: 28rpx;
  width: 80rpx;
  margin-left: 15rpx;
  display: inline-block;
}

.hot-topic-cyber-btn {
  color: var(--color);
  text-transform: uppercase;
  font-size: 18rpx;
  letter-spacing: 0.3rpx;
  position: relative;
  font-weight: 900;
  width: 100%;
  height: 100%;
  line-height: 28rpx;
  text-align: center;
}

.hot-topic-cyber-btn::after, .hot-topic-cyber-btn::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  clip-path: var(--clip);
  z-index: -1;
}

.hot-topic-cyber-btn::before {
  background: var(--shadow-primary);
  transform: translate(var(--border), 0);
}

.hot-topic-cyber-btn::after {
  background: var(--primary);
}

.hot-topic-cyber-number {
  background: var(--shadow-primary);
  color: #323232;
  font-size: 10rpx;
  font-weight: 700;
  letter-spacing: 0.1rpx;
  position: absolute;
  width: 20rpx;
  height: 8rpx;
  top: 0;
  left: 78%;
  line-height: 8rpx;
  text-align: center;
}