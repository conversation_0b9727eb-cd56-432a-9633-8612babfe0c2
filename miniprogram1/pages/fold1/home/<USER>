<!-- 设置背景 -->
<view class="gradient-background">
  <van-nav-bar class="navsetting" fixed="true" placeholder="true" style="position: relative; left: 0rpx; top: 286rpx">
    <text class="navtext" slot="left">北航</text>
    <!-- 自定义搜索框 -->
    <view slot="right" class="search-container {{guideStep === 5 && showGuideHint && !guideClosing ? 'guide-active' : ''}}">
      <input class="search-input" placeholder="芜湖" value="{{value}}" bindinput="onInput" bindconfirm="onSearch" />
    </view>
  </van-nav-bar>
  <!-- 顶部标签 -->
  <view class="grid-list {{guideStep === 5 && showGuideHint && !guideClosing ? 'guide-active' : ''}}">
    <view class="grid-item " wx:for="{{grid}}" wx:key="number" bindtap="selectItem" data-number="{{item.number}}">
      <image src="{{item.icon}}" mode="widthFix"></image>
      <text wx:if="{{selectedItemId === item.number}}" style="font-weight: bold;color: rgb(231, 112, 0);">{{item.text}}</text>
      <text wx:else style="color: black">{{item.text}}</text>
    </view>
  </view>
  <!-- 占位 -->
  <view class="zhanwei">开心快乐每一天🥳</view>
  <!-- 不同页面内容 -->
  <view class="content">
    <!-- selectedItemId === 1 的样式模板 -->
    <view wx:if="{{selectedItemId === 1}}">
      <loading show="{{isLoading}}" mask="false"></loading>
      <view wx:if="{{!isLoading}}">
        <!-- 添加每日热帖区域 -->
        <view wx:if="{{showHotTopic}}">
          <view class="hot-topic-container" bindtap="goToHot">
            <view class="hot-topic-item">
              <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
                <view style="display: flex; align-items: center;">
                  <view class="hot-topic-avatar">
                    <image src="/images/remen.png" style="width:60rpx;height:60rpx;display:block;margin:auto;" mode="aspectFit" />
                  </view>
                  <view style="display: flex; align-items: center;">
                    <text style="margin-left:15rpx; font-weight: bold;">每日热帖</text>
                    <view class="hot-view-badge">
                      <view class="hot-view-btn">
                        点击查看
                        <view class="hot-view-number">GO</view>
                      </view>
                    </view>
                  </view>
                </view>
                <image src="/images/guanbi.png" class="close-icon" catchtap="showHotTopicModal"></image>
              </view>
              <view class="hot-topic-content">
                <view class="hot-topic-list">
                  <view class="hot-topic-item-row" wx:for="{{displayTopics}}" wx:key="index">
                    <text class="hot-topic-rank">{{item.rank}}.</text>
                    <text class="hot-topic-text">{{item.content}}</text>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
        <!-- 原有的消息列表 -->
        <view wx:for="{{messages1}}" wx:key="index" class="num-item {{item.is_anonymous ? 'anonymous-item' : ''}}" bindtap="viewMessageDetail" data-index="{{index}}" style="background-color: {{item.is_anonymous ? 'rgba(240, 235, 225, 0.9)' : 'rgba(' + abc + ')'}};">
          <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
            <view style="display: flex; align-items: center;">
              <image src="{{item.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
              <view wx:if="{{!item.weizhi}}" style="display: flex; align-items: center;">
                <text style="margin-left:15rpx;">{{item.username}}</text>
                <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                  <view class="cyber-btn">
                    匿名
                    <view class="cyber-number">AN</view>
                  </view>
                </view>
                <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
              </view>

              <!-- 如果 item.weizhi 不为空，显示这段代码 -->
              <view wx:if="{{item.weizhi}}" style="display: flex; flex-direction: column;">
                <view style="display: flex; align-items: center;">
                  <text style="margin-left:15rpx;">{{item.username}}</text>
                  <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                    <view class="cyber-btn">
                      匿名
                      <view class="cyber-number">AN</view>
                    </view>
                  </view>
                  <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
                </view>
                <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{item.weizhi}}</text>
              </view>
            </view>
            <!-- 在最右边添加数据 -->
            <view>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '99'}}">寻找搭子</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '2'}}">发条说说</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '3'}}">校园交易</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '4'}}">告白倾诉</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '5'}}">拼车交流</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '6'}}">失物寻找</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '71'}}">房屋出租</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '72'}}">租房求助</text>
              <text style="margin-left:15rpx; font-size: 26rpx;color: #ecba16;" wx:if="{{item.choose === '8'}}">赚点外快</text>
            </view>
          </view>
          <view class="text">{{item.content}}</view>
          <view wx:if="{{item.images && item.images.length > 0}}">
            <view class="image-container">
              <view wx:for="{{item.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
                <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
              </view>
            </view>
          </view>
          <!-- 添加投票显示 -->
          <view wx:if="{{item.voteData}}" class="vote-container">
            <view class="divider"></view>
            <view class="vote-header">
              <view class="vote-title-row">
                <image src="/images/xingxingx.png" class="vote-title-icon"></image>
                <text class="vote-title">{{item.voteData.title}}</text>
                <view class="vote-type-tag">{{item.voteData.type === 'single' ? '单选' : '多选'}}</view>
              </view>
            </view>
          </view>
          <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
            <view class="gradient-text" style="line-height: 50rpx;">{{item.send_timestamp}}</view>
            <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
              <view style="color: rgb(226, 114, 23);">{{item.jine}}</view>
              <view class="action-item" catchtap="stopPropagation">
                <image src="/images/pinglun2.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
                <view style="margin-left: 10rpx;">{{item.total_pinglun}}</view>
              </view>
              <view class="action-item" catchtap="dolike" data-id="{{item.id}}" data-index="{{index}}">
                <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
                <view style="margin-left: 10rpx;">{{item.total_likes}}</view>
              </view>
            </view>
          </view>
        </view>
        <view style="height: 30rpx;"></view>
      </view>
    </view>

    <!-- 其他 selectedItemId 的样式调整，确保与 selectedItemId === 1 保持一致 -->
    <view wx:if="{{selectedItemId === 2}}">
      <view wx:for="{{messages2}}" wx:key="index" class="num-item {{item.is_anonymous ? 'anonymous-item' : ''}}" bindtap="viewMessageDetail" data-index="{{index}}" style="background-color: {{item.is_anonymous ? 'rgba(240, 235, 225, 0.9)' : 'rgba(' + abc + ')'}};">
        <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
          <view style="display: flex; align-items: center;">
            <image src="{{item.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
            <view wx:if="{{!item.weizhi}}" style="display: flex; align-items: center;">
              <text style="margin-left:15rpx;">{{item.username}}</text>
              <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                <view class="cyber-btn">
                  匿名
                  <view class="cyber-number">AN</view>
                </view>
              </view>
              <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
            </view>

            <!-- 如果 item.weizhi 不为空，显示这段代码 -->
            <view wx:if="{{item.weizhi}}" style="display: flex; flex-direction: column;">
              <view style="display: flex; align-items: center;">
                <text style="margin-left:15rpx;">{{item.username}}</text>
                <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                  <view class="cyber-btn">
                    匿名
                    <view class="cyber-number">AN</view>
                  </view>
                </view>
                <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
              </view>
              <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{item.weizhi}}</text>
            </view>
          </view>
        </view>
        <view class="text">{{item.content}}</view>
        <view wx:if="{{item.images && item.images.length > 0}}">
          <view class="image-container">
            <view wx:for="{{item.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
              <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
            </view>
          </view>
        </view>
        <!-- 添加投票显示 -->
        <view wx:if="{{item.voteData}}" class="vote-container">
          <view class="divider"></view>
          <view class="vote-header">
            <view class="vote-title-row">
              <image src="/images/xingxingx.png" class="vote-title-icon"></image>
              <text class="vote-title">{{item.voteData.title}}</text>
              <view class="vote-type-tag">{{item.voteData.type === 'single' ? '单选' : '多选'}}</view>
            </view>
          </view>
        </view>
        <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
          <view class="gradient-text" style="line-height: 50rpx;">{{item.send_timestamp}}</view>
          <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
            <view style="color: rgb(226, 114, 23);">{{item.jine}}</view>
            <view class="action-item" catchtap="stopPropagation">
              <image src="/images/pinglun2.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_pinglun}}</view>
            </view>
            <view class="action-item" catchtap="dolike" data-id="{{item.id}}" data-index="{{index}}">
              <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_likes}}</view>
            </view>
          </view>
        </view>
      </view>
      <view style="height: 30rpx;"></view>
    </view>

    <!-- selectedItemId === 3 的样式调整 -->
    <view wx:if="{{selectedItemId === 3}}">
      <view wx:for="{{messages3}}" wx:key="index" class="num-item {{item.is_anonymous ? 'anonymous-item' : ''}}" bindtap="viewMessageDetail" data-index="{{index}}" style="background-color: {{item.is_anonymous ? 'rgba(240, 235, 225, 0.9)' : 'rgba(' + abc + ')'}};">
        <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
          <view style="display: flex; align-items: center;">
            <image src="{{item.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
            <view wx:if="{{!item.weizhi}}" style="display: flex; align-items: center;">
              <text style="margin-left:15rpx;">{{item.username}}</text>
              <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                <view class="cyber-btn">
                  匿名
                  <view class="cyber-number">AN</view>
                </view>
              </view>
              <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
            </view>

            <!-- 如果 item.weizhi 不为空，显示这段代码 -->
            <view wx:if="{{item.weizhi}}" style="display: flex; flex-direction: column;">
              <view style="display: flex; align-items: center;">
                <text style="margin-left:15rpx;">{{item.username}}</text>
                <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                  <view class="cyber-btn">
                    匿名
                    <view class="cyber-number">AN</view>
                  </view>
                </view>
                <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
              </view>
              <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{item.weizhi}}</text>
            </view>
          </view>
        </view>
        <view class="text">{{item.content}}</view>
        <view wx:if="{{item.images && item.images.length > 0}}">
          <view class="image-container">
            <view wx:for="{{item.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
              <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
            </view>
          </view>
        </view>
        <!-- 添加投票显示 -->
        <view wx:if="{{item.voteData}}" class="vote-container">
          <view class="divider"></view>
          <view class="vote-header">
            <view class="vote-title-row">
              <image src="/images/xingxingx.png" class="vote-title-icon"></image>
              <text class="vote-title">{{item.voteData.title}}</text>
              <view class="vote-type-tag">{{item.voteData.type === 'single' ? '单选' : '多选'}}</view>
            </view>
          </view>
        </view>
        <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
          <view class="gradient-text" style="line-height: 50rpx;">{{item.send_timestamp}}</view>
          <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
            <view style="color: rgb(226, 114, 23);">{{item.jine}}</view>
            <view class="action-item" catchtap="stopPropagation">
              <image src="/images/pinglun2.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_pinglun}}</view>
            </view>
            <view class="action-item" catchtap="dolike" data-id="{{item.id}}" data-index="{{index}}">
              <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_likes}}</view>
            </view>
          </view>
        </view>
      </view>
      <view style="height: 30rpx;"></view>
    </view>

    <!-- selectedItemId === 4 的样式调整 -->
    <view wx:if="{{selectedItemId === 4}}">
      <view wx:for="{{messages4}}" wx:key="index" class="num-item {{item.is_anonymous ? 'anonymous-item' : ''}}" bindtap="viewMessageDetail" data-index="{{index}}" style="background-color: {{item.is_anonymous ? 'rgba(240, 235, 225, 0.9)' : 'rgba(' + abc + ')'}};">
        <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
          <view style="display: flex; align-items: center;">
            <image src="{{item.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
            <view wx:if="{{!item.weizhi}}" style="display: flex; align-items: center;">
              <text style="margin-left:15rpx;">{{item.username}}</text>
              <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                <view class="cyber-btn">
                  匿名
                  <view class="cyber-number">AN</view>
                </view>
              </view>
              <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
            </view>

            <!-- 如果 item.weizhi 不为空，显示这段代码 -->
            <view wx:if="{{item.weizhi}}" style="display: flex; flex-direction: column;">
              <view style="display: flex; align-items: center;">
                <text style="margin-left:15rpx;">{{item.username}}</text>
                <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                  <view class="cyber-btn">
                    匿名
                    <view class="cyber-number">AN</view>
                  </view>
                </view>
                <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
              </view>
              <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{item.weizhi}}</text>
            </view>
          </view>
        </view>
        <view class="text">{{item.content}}</view>
        <view wx:if="{{item.images && item.images.length > 0}}">
          <view class="image-container">
            <view wx:for="{{item.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
              <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
            </view>
          </view>
        </view>
        <!-- 添加投票显示 -->
        <view wx:if="{{item.voteData}}" class="vote-container">
          <view class="divider"></view>
          <view class="vote-header">
            <view class="vote-title-row">
              <image src="/images/xingxingx.png" class="vote-title-icon"></image>
              <text class="vote-title">{{item.voteData.title}}</text>
              <view class="vote-type-tag">{{item.voteData.type === 'single' ? '单选' : '多选'}}</view>
            </view>
          </view>
        </view>
        <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
          <view class="gradient-text" style="line-height: 50rpx;">{{item.send_timestamp}}</view>
          <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
            <view style="color: rgb(226, 114, 23);">{{item.jine}}</view>
            <view class="action-item" catchtap="stopPropagation">
              <image src="/images/pinglun2.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_pinglun}}</view>
            </view>
            <view class="action-item" catchtap="dolike" data-id="{{item.id}}" data-index="{{index}}">
              <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_likes}}</view>
            </view>
          </view>
        </view>
      </view>
      <view style="height: 30rpx;"></view>
    </view>
    <!-- selectedItemId === 99 的样式调整 -->
    <view wx:if="{{selectedItemId === 99}}">
      <view wx:for="{{messages99}}" wx:key="index" class="num-item {{item.is_anonymous ? 'anonymous-item' : ''}}" bindtap="viewMessageDetail" data-index="{{index}}" style="background-color: {{item.is_anonymous ? 'rgba(240, 235, 225, 0.9)' : 'rgba(' + abc + ')'}};">
        <view class="touxiang1" style="display: flex; justify-content: space-between; align-items: center;">
          <view style="display: flex; align-items: center;">
            <image src="{{item.face_url}}" style="height: 70rpx; width: 70rpx; border-radius: 15%;"></image>
            <view wx:if="{{!item.weizhi}}" style="display: flex; align-items: center;">
              <text style="margin-left:15rpx;">{{item.username}}</text>
              <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                <view class="cyber-btn">
                  匿名
                  <view class="cyber-number">AN</view>
                </view>
              </view>
              <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
            </view>

            <!-- 如果 item.weizhi 不为空，显示这段代码 -->
            <view wx:if="{{item.weizhi}}" style="display: flex; flex-direction: column;">
              <view style="display: flex; align-items: center;">
                <text style="margin-left:15rpx;">{{item.username}}</text>
                <view wx:if="{{item.is_anonymous}}" class="cyber-badge">
                  <view class="cyber-btn">
                    匿名
                    <view class="cyber-number">AN</view>
                  </view>
                </view>
                <text wx:if="{{item.titlename && item.titlename !== '无' && item.titlename !== 'undefined'}}" class="touxian2 bg{{item.titlecolor}}">{{item.titlename}}</text>
              </view>
              <text style="margin-left:15rpx; font-size: 24rpx;color: #808080;">{{item.weizhi}}</text>
            </view>
          </view>
        </view>
        <view class="text">{{item.content}}</view>
        <view wx:if="{{item.images && item.images.length > 0}}">
          <view class="image-container">
            <view wx:for="{{item.images}}" wx:for-item="image" wx:if="{{index < 3}}" wx:key="index" class="image-item">
              <image src="{{image}}" mode="aspectFill" class="uniform-image"></image>
            </view>
          </view>
        </view>
        <!-- 添加投票显示 -->
        <view wx:if="{{item.voteData}}" class="vote-container">
          <view class="divider"></view>
          <view class="vote-header">
            <view class="vote-title-row">
              <image src="/images/xingxingx.png" class="vote-title-icon"></image>
              <text class="vote-title">{{item.voteData.title}}</text>
              <view class="vote-type-tag">{{item.voteData.type === 'single' ? '单选' : '多选'}}</view>
            </view>
          </view>
        </view>
        <view class="kuang" style="display: flex; justify-content: space-between; align-items: center;">
          <view class="gradient-text" style="line-height: 50rpx;">{{item.send_timestamp}}</view>
          <view class="last" style="font-size: 25rpx; display: flex; align-items: center; margin-left: auto;">
            <view style="color: rgb(226, 114, 23);">{{item.jine}}</view>
            <view class="action-item" catchtap="stopPropagation">
              <image src="/images/pinglun2.png" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_pinglun}}</view>
            </view>
            <view class="action-item" catchtap="dolike" data-id="{{item.id}}" data-index="{{index}}">
              <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" mode="aspectFit" style="width: 35rpx; height: 35rpx; margin-left: 10rpx;" />
              <view style="margin-left: 10rpx;">{{item.total_likes}}</view>
            </view>
          </view>
        </view>
      </view>
      <view style="height: 30rpx;"></view>
    </view>
  </view>
  <!-- 回顶部按钮 -->
  <view class="fixed-button" bindtap="scrollToTop">
    <image class="custom-icon" src="/images/up-arrow.png"></image>
  </view>
  <view class="fixed-button2 {{guideStep === 2 ? 'guide-active' : ''}}" bindtap="publish">
    <image class="custom-icon" src="/images/addyeah.jpeg"></image>
  </view>
  <view class="fixed-button3 {{guideStep === 3 ? 'guide-active' : ''}}" bindtap="vxgroup">
    <image class="custom-icon" src="/images/weixin-01.png"></image>
  </view>
  <!-- 添加热门按钮 -->
  <view class="fixed-button5" bindtap="goToHot">
    <image class="custom-icon" src="/images/remen.png"></image>
  </view>
  <view class="fixed-button4 {{guideStep === 4 ? 'guide-active' : ''}}" bindtap="showGuide">
    <image class="custom-icon" src="/images/wenhao.png"></image>
  </view>
</view>

<!-- 更新公告 -->
<view class="update-notice-mask {{showUpdateNotice ? 'update-show' : 'update-hide'}} transform-gpu" wx:if="{{showUpdateNotice || noticeClosing}}" catchtouchmove="stopPropagation">
  <view class="update-mask-background transform-gpu" bindtap="{{showUpdateNotice ? 'closeUpdateNotice' : 'stopPropagation'}}"></view>
  <view animation="{{updateNoticeAnimation}}" class="update-notice-box {{showUpdateNotice ? 'notice-show' : ''}}" catchtap="stopPropagation">
    <view class="update-notice-header">
      <image src="/images/xiaoyuan.png" mode="aspectFit" class="update-notice-icon"></image>
      <text class="update-title">更新公告</text>
      <text class="update-version">Version 4.5.0</text>
      <text class="update-date">2025年4月26日</text>
    </view>
    <view class="update-notice-body">
      <text class="update-text" decode="{{true}}" space="emsp">1.新增了活动专区和公众号联动，推动小程序和公众号深度交互（这几个功能没想到这么难写，写了一个月了）
2.接下来根据大家近期的意见建议来修复bug和提升用户体验 ʕ.•᷅ࡇ•᷄.ʔ fighting！</text>
    </view>
    <view class="update-notice-footer">
      <view class="update-btn-group">
        <!-- <button class="update-btn update-btn-later" bindtap="closeUpdateNotice">我知道了</button> -->
        <button class="update-btn update-btn-check" bindtap="closeUpdateNotice">知道了！</button>
        <!-- <button class="update-btn update-btn-check" bindtap="goToGongju">去看看</button> -->
      </view>
    </view>
  </view>
</view>

<!-- 引导提示遮罩层 -->
<view class="guide-hint-mask {{showGuideHint ? 'guide-show' : 'guide-hide'}} {{guideStep === 5 ? 'step-5' : ''}}" wx:if="{{showGuideHint || guideClosing}}">
  <view class="mask-background" catchtap="stopPropagation"></view>
  <!-- 第一步：欢迎页 -->
  <view wx:if="{{guideStep === 1}}" class="guide-hint-content" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-header">
      <image src="/images/xiaoyuan.png" mode="aspectFit" class="guide-hint-icon"></image>
      <text class="guide-hint-title">灵行BUAA</text>
    </view>
    <view class="guide-hint-body">
      <text class="guide-text">uu好呀，灵行BUAA欢迎你，我是由23级在校生开发的非盈利树洞小程序，无广告/钓鱼，构建和谐健康的校园社区生态.</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn gradient-btn pulse" bindtap="nextGuideStep">开始探索 ↓</button>
    </view>
  </view>

  <!-- 第二步：发布按钮 -->
  <view wx:if="{{guideStep === 2}}" class="highlight-publish" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">✏️ 点击这里可以发布帖子，不过需要先去个人中心完成学生认证，非本校学生不能发布</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="nextGuideStep">下一步</button>
    </view>
  </view>

  <!-- 第三步：微信群功能 -->
  <view wx:if="{{guideStep === 3}}" class="highlight-nav" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">💬 点击这里可以加入树洞消息推送群，有需要的同学可以加入，开发者也在群里，有问题可以直接问或者加微信</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="nextGuideStep">下一步</button>
    </view>
  </view>

  <!-- 第四步：问号按钮说明 -->
  <view wx:if="{{guideStep === 4}}" class="highlight-content" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">❓ 点击这个可以查看说明，每个页面的说明都不一样哦！</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="nextGuideStep">下一步</button>
    </view>
  </view>

  <!-- 第五步：消息详情 -->
  <view wx:if="{{guideStep === 5}}" class="highlight-message" catchtap="stopPropagation">
    <view class="skip-guide" catchtap="skipGuide">跳过引导</view>
    <view class="guide-hint-body">
      <text class="guide-text">🔍 点击这里可以切换分类，快来发现更多有趣内容吧</text>
    </view>
    <view class="guide-hint-footer">
      <button class="guide-hint-btn" bindtap="closeGuideHint">开始体验</button>
    </view>
  </view>
</view>

<!-- 在页面底部添加发布弹窗组件 -->
<publish-popup visible="{{showPublishPopup}}" bind:close="closePublishPopup"></publish-popup>

<view wx:if="{{showPrivacy}}">
  <view>隐私弹窗内容....</view>
  <button bindtap="handleOpenPrivacyContract">查看隐私协议</button>
  <button id="agree-btn" open-type="agreePrivacyAuthorization" bindagreeprivacyauthorization="handleAgreePrivacyAuthorization">同意</button>
</view>

<!-- 热帖提示弹窗 -->
<view class="hot-topic-modal {{showHotTopicModal ? 'show' : ''}}" catchtouchmove="stopPropagation">
  <view class="modal-mask" bindtap="closeHotTopicModal"></view>
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">提示</text>
      <image src="/images/guanbi.png" class="modal-close" bindtap="closeHotTopicModal"></image>
    </view>
    <view class="modal-body">
      <text class="modal-text">如果您想关闭每日热榜，可以去右下角个人中心中的设置里关闭</text>
    </view>
    <view class="modal-footer">
      <button class="modal-btn" bindtap="closeHotTopicModal">我知道了</button>
    </view>
  </view>
</view>