// pages/liaoran/detail/index.js
import roleManager from '../../../utils/roleManager';

Page({
  data: {
    categoryName: '',
    categoryId: '',
    objectList: [],
    showAddObjectModal: false,
    newObject: { name: '', avatar: '', avatarText: '', avatarTextArray: ['文','字','头','像'], avatarColor: '#A8E6CF', avatarMode: 'text' },
    isLoading: false,
    needRefresh: false,
    isChoosingImage: false,
    canPublish: false,
    showObjectTextAvatarModal: false,
    tempObjectAvatarText: '',
    tempObjectAvatarTextArray: ['文','字','头','像'],
    tempObjectAvatarColor: '#A8E6CF',
    colorOptions: [
      { name: '浅绿', value: '#A8E6CF' },
      { name: '浅红', value: '#FFB3BA' },
      { name: '浅黄', value: '#FFDFBA' },
      { name: '浅蓝', value: '#BAE1FF' },
      { name: '浅粉', value: '#FFCCCB' }
    ]
  },

  onLoad(options) {
    const categoryId = options.id
    const categoryName = decodeURIComponent(options.name || '分类详情')

    this.setData({
      categoryId,
      categoryName,
      objectList: []
    })

    // 检查用户权限
    this.checkPermissions()

    // 获取分类详情
    this.getCategoryDetail(categoryId)
  },

  // 检查用户权限
  checkPermissions() {
    const userStatus = wx.getStorageSync('status')
    const canPublish = userStatus !== '禁言' && userStatus !== 'unverified'
    this.setData({ canPublish })
  },

  onShow() {
    // 如果正在显示添加对象弹窗或正在选择图片，不要刷新数据
    if (this.data.showAddObjectModal || this.data.isChoosingImage) {
      // 重置选择图片状态
      if (this.data.isChoosingImage) {
        this.setData({ isChoosingImage: false })
      }
      return
    }

    // 检查是否需要刷新数据
    if (this.data.needRefresh && this.data.categoryId) {
      setTimeout(() => {
        this.getCategoryDetail(this.data.categoryId)
      }, 100)
      this.setData({ needRefresh: false })
    } else if (!this.data.objectList || this.data.objectList.length === 0) {
      // 只有在对象数据完全为空时才初始加载
      if (this.data.categoryId) {
        this.getCategoryDetail(this.data.categoryId)
      }
    }
  },

  // 获取分类详情
  getCategoryDetail(categoryId) {
    this.setData({ isLoading: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ isLoading: false })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/getCategoryDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: categoryId
      },
      success: (res) => {
        if (res.data.code === 200) {
          const categoryData = res.data.data

          // 处理对象图片URL和评分格式化
          const objectList = []
          if (categoryData.objects) {
            categoryData.objects.forEach(object => {
              // 处理图片URL
              let imageUrl = null
              if (object.image_url) {
                imageUrl = object.image_url.startsWith('/uploads/')
                  ? getApp().globalData.wangz + object.image_url
                  : object.image_url
              }

              // 格式化评分为一位小数
              const formattedRating = object.avg_rating ? parseFloat(object.avg_rating).toFixed(1) : '0.0'

              // 使用后端返回的统计数据
              const totalCommentCount = object.comment_count || 0
              const ratingCount = object.rating_count || 0
              const totalPeopleCount = object.total_people_count || (totalCommentCount + ratingCount)
              const hotComment = object.hot_comment || object.description || '暂无评论'

              // 处理文字头像数组
              let avatarTextArray = ['文','字','头','像']
              if (object.avatar_text) {
                avatarTextArray = object.avatar_text.split('')
              }

              objectList.push({
                ...object,
                image_url: imageUrl, // 使用新的字段名
                avatarTextArray: avatarTextArray, // 添加文字数组
                formatted_rating: formattedRating,
                total_comment_count: totalPeopleCount,
                comment_count: totalCommentCount,
                rating_count: ratingCount,
                score_count: totalPeopleCount,
                hot_comment: hotComment
              })
            })
          }

          // 对对象按评分排序
          objectList.sort((a, b) => {
            const ratingA = parseFloat(a.avg_rating) || 0
            const ratingB = parseFloat(b.avg_rating) || 0
            return ratingB - ratingA // 降序排列
          })

          this.setData({
            category: categoryData,
            objectList: objectList,
            isLoading: false
          })
        } else {
          wx.showToast({
            title: res.data.msg || '获取失败',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      },
      fail: (err) => {
        console.error('获取分类详情失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      }
    })
  },

  // 显示添加对象弹窗
  showAddObjectModal() {
    // 检查权限
    if (!this.data.canPublish) {
      wx.showToast({
        title: '您没有权限添加对象',
        icon: 'none',
        duration: 2000
      })
      return
    }

    // 随机选择头像颜色
    const colors = ['#A8E6CF', '#FFB3BA', '#FFDFBA', '#BAE1FF', '#FFCCCB']
    const randomColor = colors[Math.floor(Math.random() * colors.length)]

    this.setData({
      showAddObjectModal: true,
      newObject: {
        name: '',
        avatar: '',
        avatarText: '',
        avatarTextArray: ['文','字','头','像'],
        avatarColor: randomColor,
        avatarMode: 'text',
        imageUploaded: false // 添加图片上传状态标志
      }
    })
  },

  // 隐藏添加对象弹窗
  hideAddObjectModal() {
    this.setData({ showAddObjectModal: false })
  },

  // 处理对象名称输入
  onObjectNameInput(e) {
    this.setData({
      'newObject.name': e.detail.value
    })
  },



  // 切换到文字头像模式
  switchObjectToTextMode() {
    // 如果图片已上传，不允许切换
    if (this.data.newObject.imageUploaded) {
      wx.showToast({
        title: '图片已上传，无法切换',
        icon: 'none'
      })
      return
    }
    this.setData({
      'newObject.avatarMode': 'text'
    })
  },

  // 切换到图片头像模式
  switchObjectToImageMode() {
    this.setData({
      'newObject.avatarMode': 'image'
    })
  },

  // 对象头像文字直接输入
  onObjectAvatarTextDirectInput(e) {
    const text = e.detail.value
    const chineseChars = text.split('').filter(char => this.isChineseChar(char))
    // 如果没有输入或没有汉字，显示默认占位文字
    const textArray = chineseChars.length > 0 ? chineseChars : ['文','字','头','像']

    this.setData({
      'newObject.avatarText': text,
      'newObject.avatarTextArray': textArray
    })
  },

  // 选择对象头像
  chooseObjectAvatar() {
    // 设置选择图片状态，防止onShow触发刷新
    this.setData({ isChoosingImage: true })

    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        this.setData({
          'newObject.avatar': res.tempFilePaths[0],
          'newObject.avatarMode': 'image', // 确保切换到图片模式
          isChoosingImage: false // 重置状态
        })
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        this.setData({ isChoosingImage: false }) // 重置状态
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },



  // 检查是否为汉字
  isChineseChar(char) {
    return /[\u4e00-\u9fa5]/.test(char)
  },

  // 获取汉字数量
  getChineseCharCount(text) {
    if (!text) return 0
    return text.split('').filter(char => this.isChineseChar(char)).length
  },



  // 添加对象
  addObject() {
    const { newObject, categoryId } = this.data

    if (!newObject.name || !newObject.name.trim()) {
      wx.showToast({
        title: '请输入对象名称',
        icon: 'none'
      })
      return
    }

    // 根据头像模式验证
    if (newObject.avatarMode === 'image' && !newObject.avatar) {
      wx.showToast({
        title: '请选择图片',
        icon: 'none'
      })
      return
    }

    if (newObject.avatarMode === 'text') {
      const chineseCharCount = this.getChineseCharCount(newObject.avatarText)
      if (chineseCharCount === 0) {
        // 如果没有输入文字，使用默认的"文字头像"
        this.setData({
          'newObject.avatarText': '文字头像',
          'newObject.avatarTextArray': ['文','字','头','像']
        })
      } else if (chineseCharCount !== 4) {
        wx.showToast({
          title: '请输入4个汉字',
          icon: 'none'
        })
        return
      }
    }

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 根据头像模式处理
    if (newObject.avatarMode === 'image' && newObject.avatar) {
      wx.showLoading({
        title: '上传头像中...',
        mask: true
      })

      wx.uploadFile({
        url: getApp().globalData.wangz + '/upload/uploadCanteenAvatar',
        filePath: newObject.avatar,
        name: 'avatar',
        header: {
          'token': token
        },
        success: (uploadRes) => {
          try {
            const uploadData = JSON.parse(uploadRes.data)

            if (uploadData.code === 200) {
              // 头像上传成功，设置上传标志并添加对象
              this.setData({
                'newObject.imageUploaded': true,
                'newObject.uploadedUrl': uploadData.data.url
              })

              wx.showLoading({
                title: '添加对象中...',
                mask: true
              })

              this.submitObjectData(newObject.name, '', categoryId, uploadData.data.url, null, null, token)
            } else {
              wx.hideLoading()
              wx.showToast({
                title: uploadData.msg || '头像上传失败',
                icon: 'none'
              })
            }
          } catch (err) {
            wx.hideLoading()
            console.error('解析头像上传响应失败:', err)
            wx.showToast({
              title: '头像上传失败',
              icon: 'none'
            })
          }
        },
        fail: (err) => {
          wx.hideLoading()
          console.error('头像上传失败:', err)
          wx.showToast({
            title: '头像上传失败，请重试',
            icon: 'none'
          })
        }
      })
    }
    // 如果是文字头像模式
    else {
      wx.showLoading({
        title: '添加对象中...',
        mask: true
      })

      // 只保存汉字部分
      const chineseChars = newObject.avatarText.split('').filter(char => this.isChineseChar(char)).join('')
      this.submitObjectData(newObject.name, '', categoryId, null, chineseChars, newObject.avatarColor, token)
    }
  },

  // 提交对象数据
  submitObjectData(objectName, description, categoryId, avatarUrl, avatarText, avatarColor, token) {
    // 获取当前用户信息
    const username = wx.getStorageSync('username') || getApp().globalData.username || '匿名用户'

    // 准备提交数据
    const submitData = {
      name: objectName.trim(),
      description: description.trim() || '',
      category_id: categoryId,
      creator_name: username
    }

    // 如果有图片头像
    if (avatarUrl) {
      submitData.image_url = avatarUrl
    }
    // 如果有文字头像
    else if (avatarText) {
      submitData.avatar_text = avatarText
      submitData.avatar_color = avatarColor
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/addObject',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: submitData,
      success: (res) => {
        wx.hideLoading()
        if (res.data.code === 200) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          })
          this.hideAddObjectModal()
          // 延迟刷新以避免闪烁
          setTimeout(() => {
            this.getCategoryDetail(categoryId)
          }, 300)
        } else {
          wx.showToast({
            title: res.data.msg || '添加失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('添加对象失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  },

  // 预览对象头像
  previewObjectAvatar(e) {
    const imageUrl = e.currentTarget.dataset.img
    if (imageUrl) {
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl]
      })
    }
  },

  // 跳转到对象详情页
  goToObjectDetail(e) {
    const objectId = e.currentTarget.dataset.id
    wx.navigateTo({
      url: `/pages/liaoran/objects/detail/index?id=${objectId}`
    })
  },

  // 设置需要刷新的标志（供其他页面调用）
  setNeedRefresh() {
    this.setData({ needRefresh: true })
  }
})
