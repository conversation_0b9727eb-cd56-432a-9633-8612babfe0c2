<view class="canteen-bg">
  <custom-nav-bar title="{{categoryName}}" back="true"></custom-nav-bar>
  <loading show="{{isLoading}}" mask="false"></loading>
  <view wx:if="{{!isLoading}}">
    <!-- 空状态 -->
    <view wx:if="{{objectList.length === 0}}" class="empty-state">
      <image src="/images/liaoran.png" class="empty-icon" mode="aspectFit"></image>
      <view class="empty-text">该分类下暂无对象，快去添加吧~</view>
      <view wx:if="{{canPublish}}" class="empty-btn" bindtap="showAddObjectModal">添加对象</view>
    </view>

    <!-- 对象列表 - 使用与餐厅列表相同的样式 -->
    <view wx:else class="msg-list">
      <view class="canteen-card" wx:for="{{objectList}}" wx:key="id" bindtap="goToObjectDetail" data-id="{{item.id}}">
        <view class="canteen-avatar large-avatar">
          <view wx:if="{{item.image_url}}" class="avatar-container">
            <image class="avatar-img" src="{{item.image_url}}" mode="aspectFill" catchtap="previewObjectAvatar" data-img="{{item.image_url}}"></image>
          </view>
          <view wx:else class="avatar-container text-avatar" style="background-color: {{item.avatar_color || '#A8E6CF'}}">
            <view class="avatar-text-grid">
              <view wx:for="{{item.avatarTextArray || ['文','字','头','像']}}" wx:key="index" class="text-char">{{item}}</view>
            </view>
          </view>
        </view>
        <view class="card-info adjust-info">
          <view class="card-header-row">
            <text class="canteen-name">{{item.name}}</text>
            <view class="total-rating-card">
              <image src="/images/xingxingx.png" class="star-icon"></image>
              <text>{{item.formatted_rating || '0.0'}}</text>
            </view>
          </view>
          <view class="canteen-second-row">
            <view class="hot-comment" wx:if="{{item.hot_comment}}">"{{item.hot_comment}}"</view>
            <view class="hot-comment" wx:else>暂无评论</view>
            <text class="score-people-text">{{item.total_comment_count || 0}}人已评分</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 右下角悬浮添加按钮 - 有权限的用户可见 -->
    <fixed-add-btn wx:if="{{canPublish}}" bind:add="showAddObjectModal" />
  </view>

  <!-- 添加对象弹窗 -->
  <view class="modal {{showAddObjectModal?'show':''}}" wx:if="{{showAddObjectModal}}">
    <view class="modal-mask" bindtap="hideAddObjectModal"></view>
    <view class="modal-content">
      <view class="modal-header">
        <view class="modal-title">添加对象</view>
      </view>
      <view class="modal-body">
        <view class="input-group">
          <text class="input-label">对象名称</text>
          <input class="modal-input" placeholder="请输入对象名称" value="{{newObject.name}}" bindinput="onObjectNameInput" />
        </view>
        <!-- 对象头像 -->
        <view class="input-group">
          <!-- 头像预览 -->
          <view class="object-avatar-preview">
            <view class="object-avatar-container text-avatar" style="background-color: {{newObject.avatarColor || '#A8E6CF'}}">
              <view wx:if="{{newObject.avatarMode === 'image' && newObject.avatar}}" class="avatar-image">
                <image class="object-avatar-img" src="{{newObject.avatar}}" mode="aspectFill"></image>
              </view>
              <view wx:else class="avatar-text-grid">
                <view wx:for="{{newObject.avatarTextArray || ['文','字','头','像']}}" wx:key="index" class="text-char">{{item}}</view>
              </view>
            </view>
          </view>

          <!-- 头像模式切换 -->
          <view class="avatar-mode-switch">
            <view class="switch-item {{newObject.avatarMode === 'text' ? 'active' : ''}} {{newObject.imageUploaded ? 'disabled' : ''}}" bindtap="switchObjectToTextMode">
              <text>文字头像</text>
            </view>
            <view class="switch-item {{newObject.avatarMode === 'image' ? 'active' : ''}}" bindtap="switchObjectToImageMode">
              <text>图片头像</text>
            </view>
          </view>

          <!-- 文字头像输入 -->
          <view wx:if="{{newObject.avatarMode === 'text'}}" class="input-group">
            <text class="input-label">文字头像（限4个字）</text>
            <input class="modal-input" placeholder="请输入4个汉字" value="{{newObject.avatarText}}" bindinput="onObjectAvatarTextDirectInput" />
          </view>

          <!-- 图片上传按钮 -->
          <view wx:if="{{newObject.avatarMode === 'image'}}" class="image-upload-section">
            <button class="upload-btn" bindtap="chooseObjectAvatar">
              <image src="/images/xiangji.png" class="upload-icon"></image>
              <text>选择图片</text>
            </button>
          </view>
        </view>
      </view>
      <view class="modal-footer">
        <button class="btn-cancel" bindtap="hideAddObjectModal">取消</button>
        <button class="btn-confirm" bindtap="addObject">确定</button>
      </view>
    </view>
  </view>


</view>
