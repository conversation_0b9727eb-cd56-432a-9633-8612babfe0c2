/* pages/liaoran/detail/index.wxss */
/* 引用canteen详情页的样式 */
@import "/pages/canteen/detail/index.wxss";

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 120rpx 0 80rpx 0;
}

.empty-icon {
  width: 220rpx;
  height: 220rpx;
  margin-bottom: 32rpx;
  opacity: 0.85;
}

.empty-text {
  color: #7a8fa6;
  font-size: 30rpx;
  margin-bottom: 40rpx;
  font-weight: 500;
}

.empty-btn {
  background: linear-gradient(90deg, #5ec6fa 0%, #3487ef 100%);
  color: #fff;
  padding: 20rpx 80rpx;
  border-radius: 40rpx;
  font-size: 30rpx;
  font-weight: 600;
  box-shadow: 0 8rpx 24rpx rgba(52,135,239,0.13);
  transition: all 0.3s;
  letter-spacing: 2rpx;
}

.empty-btn:active {
  transform: scale(0.97);
  box-shadow: 0 4rpx 12rpx rgba(52,135,239,0.10);
}

/* 覆盖弹窗样式，确保和canteen完全一致 */
.modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1000;
  visibility: hidden;
  opacity: 0;
  transition: all 0.3s ease;
}

.modal.show {
  visibility: visible;
  opacity: 1;
}

.modal-mask {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0,0,0,0.6);
  backdrop-filter: blur(4rpx);
}

.modal-content {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%) scale(0.95);
  width: 600rpx;
  background: #fff;
  border-radius: 32rpx;
  overflow: hidden;
  transition: all 0.3s ease;
}

.modal.show .modal-content {
  transform: translate(-50%, -50%) scale(1);
}

.modal-header {
  padding: 40rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  border-bottom: 2rpx solid #f5f5f5;
}

.modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #222;
}

.modal-body {
  padding: 40rpx;
}

.input-group {
  margin-bottom: 32rpx;
}

.input-label {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
}

.modal-input {
  width: 100%;
  height: 88rpx;
  background: #f7f8fa;
  border-radius: 16rpx;
  padding: 0 24rpx;
  font-size: 28rpx;
  color: #222;
  box-sizing: border-box;
  border: none;
}

.upload-btn {
  width: 120rpx;
  height: 120rpx;
  background: #f7f8fa;
  border-radius: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 2rpx dashed #2d8cf0;
  margin-top: 8rpx;
}

.upload-btn image {
  width: 80rpx;
  height: 80rpx;
}

.modal-footer {
  padding: 32rpx 40rpx;
  display: flex;
  gap: 24rpx;
  border-top: 2rpx solid #f5f5f5;
}

.btn-cancel {
  flex: 1;
  height: 88rpx;
  background: #f7f8fa;
  border-radius: 44rpx;
  color: #666;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

.btn-confirm {
  flex: 1;
  height: 88rpx;
  background: #2d8cf0;
  border-radius: 44rpx;
  color: #fff;
  font-size: 28rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  border: none;
}

/* 覆盖canteen样式，确保头像容器大小一致 */
.canteen-avatar {
  width: 140rpx !important;
  height: 140rpx !important;
  border-radius: 18rpx !important;
  overflow: hidden;
  flex-shrink: 0;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.06);
  align-self: center;
}

/* 确保文字头像容器也是正确大小 */
.avatar-container {
  width: 140rpx !important;
  height: 140rpx !important;
  border-radius: 18rpx !important;
  overflow: hidden;
}

/* 对象头像相关样式 */
.object-avatar-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 24rpx;
}

.object-avatar-container {
  width: 140rpx;
  height: 140rpx;
  border-radius: 18rpx;
  overflow: hidden;
  position: relative;
}

.object-avatar-img {
  width: 100%;
  height: 100%;
}

.avatar-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.text-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 头像模式切换 */
.avatar-mode-switch {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
  margin: 24rpx 0;
}

.switch-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.switch-item.active {
  background: #5ec6fa;
  color: #fff;
  font-weight: 600;
}

.switch-item.disabled {
  background: #f0f0f0;
  color: #ccc;
  cursor: not-allowed;
}

/* 文字头像输入 */
.text-avatar-input {
  margin-top: 24rpx;
}

.input-container {
  display: flex;
  align-items: center;
  position: relative;
}

.char-hint {
  position: absolute;
  right: 16rpx;
  font-size: 24rpx;
  color: #999;
}

/* 图片上传区域 */
.image-upload-section {
  margin-top: 24rpx;
  text-align: center;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 32rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background: #f8f9fa;
  color: #666;
  font-size: 28rpx;
}

.upload-btn:active {
  background: #e9ecef;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
}

/* 文字头像网格布局 */
.avatar-text-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 4rpx;
  width: 100%;
  height: 100%;
  padding: 8rpx;
  box-sizing: border-box;
}

.text-char {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}

/* 文字头像弹窗样式 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1100;
  display: flex;
  align-items: center;
  justify-content: center;
}

.modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #999;
  border-radius: 50%;
  background: #f8f9fa;
}

.text-avatar-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.preview-avatar {
  width: 160rpx;
  height: 160rpx;
  border-radius: 16rpx;
}

.input-field {
  width: 100%;
  padding: 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  font-size: 28rpx;
  color: #333;
  box-sizing: border-box;
}

.char-hint {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
  text-align: right;
}

.color-options {
  display: flex;
  gap: 16rpx;
  flex-wrap: wrap;
}

.color-item {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  border: 4rpx solid transparent;
  transition: all 0.3s ease;
}

.color-item.selected {
  border-color: #5ec6fa;
  transform: scale(1.1);
}

.color-check {
  color: #fff;
  font-size: 32rpx;
  font-weight: bold;
  text-shadow: 0 0 4rpx rgba(0, 0, 0, 0.5);
}

.modal-btn {
  flex: 1;
  height: 80rpx;
  border-radius: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  font-weight: 600;
  transition: all 0.3s ease;
}

.modal-btn.cancel {
  background: #f8f9fa;
  color: #666;
}

.modal-btn.confirm {
  background: #5ec6fa;
  color: #fff;
}

.modal-btn:active {
  transform: scale(0.95);
}