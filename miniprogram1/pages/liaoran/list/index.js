// pages/liaoran/list/index.js
import roleManager from '../../../utils/roleManager';

Page({
  data: {
    categoryList: [],
    isLoading: false,
    canPublish: false
  },

  onLoad(options) {
    // 检查用户权限
    this.checkPermissions()
    // 获取分类列表
    this.getCategoryList()
  },

  onShow() {
    // 每次显示页面时刷新列表
    this.getCategoryList()
  },

  // 检查用户权限
  checkPermissions() {
    const userStatus = wx.getStorageSync('status')
    const canPublish = userStatus !== '禁言' && userStatus !== 'unverified'
    this.setData({ canPublish })
  },

  // 获取分类列表
  getCategoryList() {
    this.setData({ isLoading: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ isLoading: false })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/getCategoryList',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 处理后端返回的数据格式
          const categoryList = res.data.data.map(category => {
            // 处理图片URL
            let imageUrl = null
            if (category.image_url) {
              imageUrl = category.image_url.startsWith('/uploads/')
                ? getApp().globalData.wangz + category.image_url
                : category.image_url
            }

            // 格式化评分为一位小数
            const formattedRating = category.avg_rating ? parseFloat(category.avg_rating).toFixed(1) : '0.0'

            // 处理文字头像数组
            let avatarTextArray = ['文','字','头','像']
            if (category.avatar_text) {
              avatarTextArray = category.avatar_text.split('')
            }

            return {
              ...category,
              image_url: imageUrl, // 使用新的字段名
              avatarTextArray: avatarTextArray, // 添加文字数组
              formatted_rating: formattedRating,
              total_comment_count: category.total_people_count || 0,
              hot_comment: category.description || '暂无描述'
            }
          })

          // 按评分从高到低排序
          categoryList.sort((a, b) => {
            const ratingA = parseFloat(a.avg_rating) || 0
            const ratingB = parseFloat(b.avg_rating) || 0
            return ratingB - ratingA // 降序排列
          })

          this.setData({
            categoryList: categoryList,
            isLoading: false
          })
        } else {
          wx.showToast({
            title: res.data.msg || '获取失败',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      },
      fail: (err) => {
        console.error('获取分类列表失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      }
    })
  },

  // 跳转到发布分类页面
  goToPublish() {
    wx.navigateTo({
      url: '/pages/liaoran/publish/index'
    })
  },

  // 跳转到分类详情页
  goToDetail(e) {
    const categoryId = e.currentTarget.dataset.id
    const category = this.data.categoryList.find(item => item.id === categoryId)
    if (category) {
      wx.navigateTo({
        url: `/pages/liaoran/detail/index?id=${categoryId}&name=${encodeURIComponent(category.name)}`
      })
    }
  },

  // 预览分类头像
  previewAvatar(e) {
    const imageUrl = e.currentTarget.dataset.img
    if (imageUrl && imageUrl !== '/images/liaoran.png') {
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl]
      })
    }
  }
})
