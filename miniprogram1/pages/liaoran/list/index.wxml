<view class="canteen-bg">
  <custom-nav-bar title="了然几分" back="true"></custom-nav-bar>
  <loading show="{{isLoading}}" mask="false"></loading>

  <view wx:if="{{!isLoading}}" class="content">
    <!-- 空状态 -->
    <view wx:if="{{categoryList.length === 0}}" class="empty-state">
      <image src="/images/liaoran.png" class="empty-icon" mode="aspectFit"></image>
      <view class="empty-text">暂无分类，快去添加吧~</view>
      <view wx:if="{{canPublish}}" class="empty-btn" bindtap="goToPublish">添加分类</view>
    </view>

    <!-- 消息流容器，两侧留白 -->
    <view wx:else class="msg-list">
      <view class="canteen-card" wx:for="{{categoryList}}" wx:key="id" bindtap="goToDetail" data-id="{{item.id}}">
        <view class="canteen-avatar large-avatar">
          <view wx:if="{{item.image_url}}" class="avatar-container">
            <image class="avatar-img" src="{{item.image_url}}" mode="aspectFill" catchtap="previewAvatar" data-img="{{item.image_url}}"></image>
          </view>
          <view wx:else class="avatar-container text-avatar" style="background-color: {{item.avatar_color || '#A8E6CF'}}">
            <view class="avatar-text-grid">
              <view wx:for="{{item.avatarTextArray || ['文','字','头','像']}}" wx:key="index" class="text-char">{{item}}</view>
            </view>
          </view>
        </view>
        <view class="card-info adjust-info">
          <view class="card-header-row">
            <text class="canteen-name">{{item.name}}</text>
            <view class="total-rating-card">
              <image src="/images/xingxingx.png" class="star-icon"></image>
              <text>{{item.formatted_rating || '0.0'}}</text>
            </view>
          </view>
          <view class="canteen-second-row">
            <view class="hot-comment" wx:if="{{item.hot_comment}}">"{{item.hot_comment}}"</view>
            <view class="hot-comment" wx:else>暂无描述</view>
            <text class="score-people-text">{{item.total_comment_count || 0}}人已评分</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 添加按钮 - 有权限的用户可见 -->
    <fixed-add-btn wx:if="{{canPublish}}" bind:add="goToPublish" />
  </view>
</view>
