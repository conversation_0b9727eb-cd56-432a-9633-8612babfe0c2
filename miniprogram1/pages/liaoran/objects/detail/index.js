const app = getApp()

// iOS兼容的日期转换函数
function createIOSCompatibleDate(timeStr) {
  if (!timeStr) return new Date()

  // 将 "2025-05-29 10:55:03" 格式转换为 "2025/05/29 10:55:03"
  // iOS支持的格式
  const iosCompatibleStr = timeStr.replace(/-/g, '/')
  return new Date(iosCompatibleStr)
}

Page({
  data: {
    objectInfo: {},
    comments: [],
    currentRating: 0,
    sortType: 'hot', // hot: 热门, latest: 最新
    isLoading: true,
    totalRating: 0,
    ratingCount: 0,
    showRatingModal: false,
    tempRating: 0,
    commentSectionVisible: false,
    commentInputFocused: false,
    commentContent: '',
    commentPlaceholder: '说点什么...',
    replyToComment: null,
    isSubmittingComment: false,
    keyboardHeight: 0,
    showEmoji: false,
    showEmojiList: false,
    quickEmojiList: ['😊', '😂', '😘', '😍', '😭', '🥰', '😎', '😋'],
    // 图片相关
    selectedImages: [],
    maxImages: 3,
    emojiList: [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
      '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
      '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐'
    ]
  },

  onLoad(options) {
    const objectId = options.id
    const commentId = options.comment_id
    const replyId = options.reply_id

    if (objectId) {
      this.loadObjectDetail(objectId, commentId, replyId)
    }
  },

  // 页面显示时重新处理用户评分状态 - 使用原始user_rating
  onShow() {
    if (this.data.objectInfo && this.data.objectInfo.user_rating !== undefined) {
      let currentRating = 0
      if (this.data.objectInfo.user_rating !== null && this.data.objectInfo.user_rating !== undefined) {
        const userRating = this.data.objectInfo.user_rating
        if (userRating <= -2) {
          currentRating = (userRating + 12) / 2
        } else if (userRating >= 2) {
          currentRating = (userRating / 2) + 5
        }
      }
      this.setData({
        currentRating: currentRating
      })
    }
  },

  loadObjectDetail(objectId, commentId, replyId) {
    this.setData({ isLoading: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ isLoading: false })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/getObjectDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: objectId
      },
      success: (res) => {
        if (res.data.code === 200) {
          const objectData = res.data.data

          // 处理对象头像URL
          let avatarUrl = null
          if (objectData.image_url) {
            avatarUrl = objectData.image_url.startsWith('/uploads/')
              ? getApp().globalData.wangz + objectData.image_url
              : objectData.image_url
          }

          // 处理评论用户头像URL和时间格式化
          const comments = (objectData.comments || []).map(comment => {
            let userAvatar = comment.user.avatar || '/images/default_avatar.png'
            if (userAvatar.startsWith('/uploads/')) {
              userAvatar = getApp().globalData.wangz + userAvatar
            }

            // 处理回复的头像和时间
            const replies = (comment.replies || []).map(reply => {
              let replyAvatar = reply.user.avatar || '/images/default_avatar.png'
              if (replyAvatar.startsWith('/uploads/')) {
                replyAvatar = getApp().globalData.wangz + replyAvatar
              }
              return {
                ...reply,
                user: {
                  ...reply.user,
                  avatar: replyAvatar
                }
              }
            })

            return {
              ...comment,
              user: {
                ...comment.user,
                avatar: userAvatar
              },
              replies: replies
            }
          })

          // 确保评分显示为x.x格式
          const formatRating = (rating) => {
            if (rating === null || rating === undefined || rating === 0) {
              return '0.0'
            }
            return parseFloat(rating).toFixed(1)
          }

          // 处理用户已评分状态 - 使用后端返回的user_rating原始值
          let currentRating = 0
          if (objectData.user_rating !== null && objectData.user_rating !== undefined) {
            // 将后端的-10到10转换为前端的1到10
            const userRating = objectData.user_rating
            if (userRating <= -2) {
              // 负数评分：-10->1, -8->2, -6->3, -4->4, -2->5
              currentRating = (userRating + 12) / 2
            } else if (userRating >= 2) {
              // 正数评分：2->6, 4->7, 6->8, 8->9, 10->10
              currentRating = (userRating / 2) + 5
            }
          }

          // 处理文字头像数组
          let avatarTextArray = ['文','字','头','像']
          if (objectData.avatar_text) {
            avatarTextArray = objectData.avatar_text.split('')
          }

          this.setData({
            objectInfo: {
              ...objectData,
              image_url: avatarUrl,
              avatarTextArray: avatarTextArray, // 添加文字数组
              rating: formatRating(objectData.avg_rating), // 格式化评分
              comments: comments, // 将评论数据放到objectInfo中
              tag: objectData.creator_name || '匿名用户'
            },
            totalRating: objectData.avg_rating,
            ratingCount: objectData.rating_count,
            currentRating: currentRating, // 设置用户当前评分
            isLoading: false
          })

          // 如果有commentId或replyId参数，定位到指定评论
          if (commentId || replyId) {
            setTimeout(() => {
              this.scrollToTargetComment(commentId, replyId, comments)
            }, 500) // 等待页面渲染完成
          }
        } else {
          wx.showToast({
            title: res.data.msg || '获取失败',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      },
      fail: (err) => {
        console.error('获取对象详情失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      }
    })
  },

  // 切换排序方式
  switchSort(e) {
    const sortType = e.currentTarget.dataset.type
    this.setData({ sortType })

    let sortedComments = [...(this.data.objectInfo.comments || [])]
    if (sortType === 'hot') {
      // 按点赞数排序（降序）
      sortedComments.sort((a, b) => (b.likes || 0) - (a.likes || 0))
    } else {
      // 按时间排序（最新在前）
      sortedComments.sort((a, b) => createIOSCompatibleDate(b.time) - createIOSCompatibleDate(a.time))
    }

    this.setData({
      'objectInfo.comments': sortedComments
    })
  },

  // 设置评分
  setRating(e) {
    const rating = e.currentTarget.dataset.rating
    // 转换为-10到10的评分范围
    // 1->-10, 2->-8, 3->-6, 4->-4, 5->-2, 6->2, 7->4, 8->6, 9->8, 10->10
    let actualRating
    if (rating <= 5) {
      // 负数评分：1->-10, 2->-8, 3->-6, 4->-4, 5->-2
      actualRating = (rating * 2) - 12
    } else {
      // 正数评分：6->2, 7->4, 8->6, 9->8, 10->10
      actualRating = (rating - 5) * 2
    }

    this.setData({
      tempRating: rating,
      currentRating: rating
    })

    // 立即提交评分
    this.submitRatingDirect(actualRating)
  },

  // 重置评分
  resetRating() {
    this.setData({
      currentRating: 0,
      tempRating: 0
    })
    wx.showToast({
      title: '已重置评分',
      icon: 'success',
      duration: 1500
    })
  },

  // 直接提交评分（仅评分，不包含评论）
  submitRatingDirect(rating) {
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/addRating',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        object_id: this.data.objectInfo.id,
        rating: rating
      },
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '评分成功',
            icon: 'success'
          })
          // 通知上级页面需要刷新
          this.notifyParentPageRefresh()
          // 局部更新评分数据，避免页面闪烁 - 完全按照windows/detail逻辑
          this.updateRatingData()
        } else {
          wx.showToast({
            title: res.data.msg || '评分失败',
            icon: 'none'
          })
          // 重置评分状态
          this.setData({
            currentRating: 0,
            tempRating: 0
          })
        }
      },
      fail: (err) => {
        console.error('提交评分失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 只更新平均评分和评分人数，不更新用户评分
  updateAverageRatingOnly() {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/getObjectDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: this.data.objectInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          const newData = res.data.data
          // 只更新平均评分和评分人数，不更新用户评分
          this.setData({
            'objectInfo.avg_rating': newData.avg_rating,
            'objectInfo.rating_count': newData.rating_count,
            totalRating: newData.avg_rating,
            ratingCount: newData.rating_count
          })
        }
      },
      fail: (err) => {
        console.error('更新平均评分失败:', err)
      }
    })
  },

  // 局部更新评分数据 - 完全按照windows/detail逻辑
  updateRatingData() {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/getObjectDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: this.data.objectInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          const newData = res.data.data
          // 只更新评分相关数据，保持其他数据不变 - 使用正确的字段名
          this.setData({
            'objectInfo.avg_rating': newData.avg_rating,
            'objectInfo.rating_count': newData.rating_count,
            'objectInfo.user_rating': newData.user_rating
          })
        }
      },
      fail: (err) => {
        console.error('更新评分数据失败:', err)
      }
    })
  },

  // 局部更新评论数据
  updateCommentsData(scrollToNewComment = false) {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    // 获取当前用户ID，用于定位新评论
    const currentUserId = wx.getStorageSync('user_id')

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/getObjectDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: this.data.objectInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          const newData = res.data.data

          // 处理评论用户头像URL
          const comments = (newData.comments || []).map(comment => {
            let userAvatar = comment.user.avatar || '/images/default_avatar.png'
            if (userAvatar.startsWith('/uploads/')) {
              userAvatar = getApp().globalData.wangz + userAvatar
            }

            // 处理回复的头像
            const replies = (comment.replies || []).map(reply => {
              let replyAvatar = reply.user.avatar || '/images/default_avatar.png'
              if (replyAvatar.startsWith('/uploads/')) {
                replyAvatar = getApp().globalData.wangz + replyAvatar
              }
              return {
                ...reply,
                user: {
                  ...reply.user,
                  avatar: replyAvatar
                }
              }
            })

            return {
              ...comment,
              user: {
                ...comment.user,
                avatar: userAvatar
              },
              replies: replies
            }
          })

          // 只更新评论相关数据，保持其他数据不变
          this.setData({
            'objectInfo.comments': comments
          })

          // 如果需要滚动到新评论
          if (scrollToNewComment && currentUserId) {
            setTimeout(() => {
              this.scrollToUserLatestComment(currentUserId, comments)
            }, 100) // 等待DOM更新
          }
        }
      },
      fail: (err) => {
        console.error('更新评论数据失败:', err)
      }
    })
  },

  // 滚动到用户最新评论
  scrollToUserLatestComment(userId, comments) {
    try {
      // 查找用户的最新评论（包括回复）
      let targetCommentIndex = -1
      let targetReplyIndex = -1
      let latestTime = 0

      comments.forEach((comment, commentIndex) => {
        // 检查主评论
        if (comment.user.id == userId) {
          const commentTime = createIOSCompatibleDate(comment.time).getTime()
          if (commentTime > latestTime) {
            latestTime = commentTime
            targetCommentIndex = commentIndex
            targetReplyIndex = -1
          }
        }

        // 检查回复
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.forEach((reply, replyIndex) => {
            if (reply.user.id == userId) {
              const replyTime = createIOSCompatibleDate(reply.time).getTime()
              if (replyTime > latestTime) {
                latestTime = replyTime
                targetCommentIndex = commentIndex
                targetReplyIndex = replyIndex
              }
            }
          })
        }
      })

      // 如果找到了用户的评论，滚动到该位置
      if (targetCommentIndex >= 0) {
        let scrollId
        if (targetReplyIndex >= 0) {
          // 滚动到回复
          scrollId = `reply-${targetCommentIndex}-${targetReplyIndex}`
        } else {
          // 滚动到主评论
          scrollId = `comment-${targetCommentIndex}`
        }

        wx.pageScrollTo({
          selector: `#${scrollId}`,
          duration: 300
        })
      }
    } catch (error) {
      console.error('滚动到评论失败:', error)
    }
  },

  // 预览对象头像
  previewObjectAvatar(e) {
    const imageUrl = e.currentTarget.dataset.img
    if (imageUrl) {
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl]
      })
    }
  },

  // 通知上级页面需要刷新
  notifyParentPageRefresh() {
    try {
      const pages = getCurrentPages()
      if (pages.length >= 2) {
        const prevPage = pages[pages.length - 2]
        if (prevPage && typeof prevPage.setNeedRefresh === 'function') {
          prevPage.setNeedRefresh()
        }
      }
    } catch (error) {
      console.error('通知上级页面刷新失败:', error)
    }
  },

  // 立即添加新评论到本地数据
  addNewCommentToLocal(newComment) {
    const comments = [...(this.data.objectInfo.comments || [])]

    if (newComment.parent_id) {
      // 这是一个回复
      const parentIndex = comments.findIndex(c => c.id === newComment.parent_id)
      if (parentIndex >= 0) {
        if (!comments[parentIndex].replies) {
          comments[parentIndex].replies = []
        }
        comments[parentIndex].replies.push(newComment)
      }
    } else {
      // 这是一个主评论
      comments.unshift(newComment) // 添加到开头
    }

    this.setData({
      'objectInfo.comments': comments
    })
  },

  // 处理评论点击
  handleCommentClick() {
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: '说点什么...',
      replyToComment: null
    })
  },

  // 隐藏评论区域
  hideCommentSection() {
    this.setData({
      commentSectionVisible: false,
      commentInputFocused: false,
      commentContent: '',
      replyToComment: null,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [], // 重置图片
      isSubmittingComment: false // 重置发送状态
    })
  },

  // 点击评论进行回复
  clickCommentToReply(e) {
    const comment = e.currentTarget.dataset.comment
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${comment.user.name}`,
      replyToComment: comment
    })
  },

  // 点击回复进行回复
  clickReplyToReply(e) {
    const reply = e.currentTarget.dataset.reply
    const commentIndex = e.currentTarget.dataset.commentIndex
    const comment = this.data.objectInfo.comments[commentIndex]

    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${reply.user.name}`,
      replyToComment: {
        ...comment,
        replyToUser: reply.user
      }
    })
  },

  // 回复评论
  replyComment(e) {
    const comment = e.currentTarget.dataset.comment
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${comment.user.name}`,
      replyToComment: comment
    })
  },

  // 评论输入
  onCommentInput(e) {
    this.setData({
      commentContent: e.detail.value
    })
  },

  // 处理输入框获取焦点
  handleInputFocus() {
    this.setData({
      showEmoji: false,
      showEmojiList: false,
      commentInputFocused: true,
      commentSectionVisible: true
    })
  },

  // 输入框失去焦点
  handleInputBlur() {
    setTimeout(() => {
      // 只有当表情面板都没显示时，才重置状态
      if (!this.data.showEmoji && !this.data.showEmojiList) {
        this.setData({
          commentInputFocused: false,
          keyboardHeight: 0
        })
      }
    }, 100)
  },

  // 键盘高度变化
  handleKeyboardHeightChange(e) {
    this.setData({
      keyboardHeight: e.detail.height
    })
  },

  // 提交评论
  submitComment() {
    const content = this.data.commentContent.trim()
    const images = this.data.selectedImages

    if (!content && images.length === 0) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      })
      return
    }

    if (this.data.isSubmittingComment) {
      return
    }

    this.setData({ isSubmittingComment: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      this.setData({ isSubmittingComment: false })
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 准备提交数据
    const submitData = {
      object_id: this.data.objectInfo.id,
      content: content
    }

    // 如果是回复评论
    if (this.data.replyToComment) {
      submitData.parent_id = this.data.replyToComment.id
      if (this.data.replyToComment.replyToUser) {
        submitData.reply_to_user_id = this.data.replyToComment.replyToUser.id
      }
    }

    // 如果有图片，先上传图片
    if (images.length > 0) {
      this.uploadImagesAndSubmitComment(submitData, images, token)
    } else {
      this.submitCommentData(submitData, token)
    }
  },

  // 上传图片并提交评论
  uploadImagesAndSubmitComment(submitData, images, token) {
    const uploadPromises = images.map((imagePath, index) => {
      return new Promise((resolve) => {  // 改为总是resolve，不reject
        wx.uploadFile({
          url: getApp().globalData.wangz + '/upload/uploadImage',  // 使用正确的接口路径
          filePath: imagePath,
          name: 'file',
          formData: {
            user_id: getApp().globalData.user_id  // 添加用户ID
          },
          header: {
            'token': token
          },
          success: (res) => {
            console.log(`图片${index + 1}上传响应:`, res.data)  // 添加日志
            try {
              const data = JSON.parse(res.data)
              // 兼容多种返回格式
              if (data.error_code === 0) {
                // 格式1: {error_code: 0, data: {urls: [...]}}
                if (data.data && data.data.urls && data.data.urls.length > 0) {
                  resolve({ success: true, url: data.data.urls[0] })
                }
                // 格式2: {error_code: 0, image_url: "..."}
                else if (data.image_url) {
                  resolve({ success: true, url: data.image_url })
                }
                // 格式3: {error_code: 0, data: {image_url: "..."}}
                else if (data.data && data.data.image_url) {
                  resolve({ success: true, url: data.data.image_url })
                }
                else {
                  resolve({ success: false, error: '返回数据格式错误' })
                }
              }
              // 格式4: {code: 200, data: {url: "..."}}
              else if (data.code === 200) {
                if (data.data && data.data.url) {
                  resolve({ success: true, url: data.data.url })
                } else {
                  resolve({ success: false, error: '返回数据格式错误' })
                }
              }
              else {
                resolve({ success: false, error: data.msg || data.message || '上传失败' })
              }
            } catch (e) {
              console.error('JSON解析失败:', e, '原始数据:', res.data)
              resolve({ success: false, error: '解析响应失败: ' + e.message })
            }
          },
          fail: (err) => {
            console.error(`图片${index + 1}上传请求失败:`, err)
            resolve({ success: false, error: err.errMsg || '上传失败' })
          }
        })
      })
    })

    Promise.all(uploadPromises)
      .then(results => {
        console.log('图片上传结果:', results)

        // 筛选出成功上传的图片
        const successfulUploads = results.filter(result => result.success)
        const failedUploads = results.filter(result => !result.success)

        if (failedUploads.length > 0) {
          console.warn('部分图片上传失败:', failedUploads)
          wx.showToast({
            title: `${failedUploads.length}张图片上传失败`,
            icon: 'none',
            duration: 2000
          })
        }

        if (successfulUploads.length > 0) {
          // 有成功上传的图片，继续提交评论
          const imageUrls = successfulUploads.map(result => result.url)
          console.log('成功上传的图片:', imageUrls)
          submitData.images = JSON.stringify(imageUrls)
          this.submitCommentData(submitData, token)
        } else {
          // 所有图片都上传失败
          wx.showToast({
            title: '所有图片上传失败',
            icon: 'none'
          })
          this.setData({ isSubmittingComment: false })
        }
      })
  },

  // 提交评论数据
  submitCommentData(submitData, token) {
    wx.request({
      url: getApp().globalData.wangz + '/liaoran/addComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: submitData,
      success: (res) => {
        this.setData({ isSubmittingComment: false })
        if (res.data.code === 200) {
          wx.showToast({
            title: '评论成功',
            icon: 'success',
            duration: 1000
          })

          // 隐藏评论输入框
          this.hideCommentSection()

          // 刷新评论数据并滚动到新评论
          setTimeout(() => {
            this.updateCommentsData(true)
          }, 500)
        } else {
          wx.showToast({
            title: res.data.msg || '评论失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        this.setData({ isSubmittingComment: false })
        console.error('提交评论失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 点赞评论
  likeComment(e) {
    const commentId = e.currentTarget.dataset.id
    const commentIndex = e.currentTarget.dataset.index

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/likeComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        comment_id: commentId
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地评论的点赞状态
          const comments = [...this.data.objectInfo.comments]
          if (comments[commentIndex]) {
            comments[commentIndex].is_liked = res.data.data.is_liked
            comments[commentIndex].likes = res.data.data.total_likes
          }

          this.setData({
            'objectInfo.comments': comments
          })

          // 显示提示
          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('点赞失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 点赞回复
  likeReply(e) {
    const replyId = e.currentTarget.dataset.replyId
    const commentIndex = e.currentTarget.dataset.commentIndex
    const replyIndex = e.currentTarget.dataset.replyIndex

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/likeReply',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        reply_id: replyId
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地回复的点赞状态
          const comments = [...this.data.objectInfo.comments]
          if (comments[commentIndex] && comments[commentIndex].replies[replyIndex]) {
            comments[commentIndex].replies[replyIndex].is_liked = res.data.data.is_liked
            comments[commentIndex].replies[replyIndex].likes = res.data.data.total_likes
          }

          this.setData({
            'objectInfo.comments': comments
          })

          // 显示提示
          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('点赞回复失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 预览评论图片
  previewCommentImage(e) {
    const imageUrl = e.currentTarget.dataset.url
    const images = e.currentTarget.dataset.images
    wx.previewImage({
      current: imageUrl,
      urls: images
    })
  },

  // 选择图片
  chooseImage() {
    const remainingCount = this.data.maxImages - this.data.selectedImages.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多只能选择${this.data.maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath)
        this.setData({
          selectedImages: [...this.data.selectedImages, ...tempFiles]
        })
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 打开相机
  openCamera() {
    const remainingCount = this.data.maxImages - this.data.selectedImages.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多只能选择${this.data.maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath)
        this.setData({
          selectedImages: [...this.data.selectedImages, ...tempFiles]
        })
      },
      fail: (err) => {
        console.error('拍照失败:', err)
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        })
      }
    })
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    wx.previewImage({
      current: this.data.selectedImages[index],
      urls: this.data.selectedImages
    })
  },

  // 删除图片
  removeImage(e) {
    const index = e.currentTarget.dataset.index
    const selectedImages = [...this.data.selectedImages]
    selectedImages.splice(index, 1)
    this.setData({
      selectedImages
    })
  },

  // 切换表情面板
  toggleEmoji() {
    if (this.data.showEmoji) {
      this.setData({
        showEmoji: false,
        commentInputFocused: true
      })
    } else {
      this.setData({
        showEmoji: true,
        showEmojiList: false,
        commentInputFocused: false,
        keyboardHeight: 0
      })
    }
  },

  // 切换emoji表情列表
  toggleEmojiList() {
    if (this.data.showEmojiList) {
      this.setData({
        showEmojiList: false,
        commentInputFocused: true
      })
    } else {
      this.setData({
        showEmojiList: true,
        showEmoji: false,
        commentInputFocused: false,
        keyboardHeight: 0
      })
    }
  },

  // 选择表情
  selectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji
    const content = this.data.commentContent + emoji
    this.setData({
      commentContent: content
    })
  },

  // 定位到指定评论
  scrollToTargetComment(commentId, replyId, comments) {
    try {
      let targetCommentIndex = -1
      let targetReplyIndex = -1

      // 查找目标评论或回复
      comments.forEach((comment, commentIndex) => {
        if (replyId) {
          // 查找回复
          if (comment.replies && comment.replies.length > 0) {
            comment.replies.forEach((reply, replyIndex) => {
              if (reply.id == replyId) {
                targetCommentIndex = commentIndex
                targetReplyIndex = replyIndex
              }
            })
          }
        } else if (commentId) {
          // 查找评论
          if (comment.id == commentId) {
            targetCommentIndex = commentIndex
          }
        }
      })

      // 如果找到了目标，滚动到该位置
      if (targetCommentIndex >= 0) {
        let scrollId
        if (targetReplyIndex >= 0) {
          // 滚动到回复
          scrollId = `reply-${targetCommentIndex}-${targetReplyIndex}`
        } else {
          // 滚动到主评论
          scrollId = `comment-${targetCommentIndex}`
        }

        wx.pageScrollTo({
          selector: `#${scrollId}`,
          duration: 300
        })

        // 添加高亮效果
        setTimeout(() => {
          this.highlightComment(scrollId)
        }, 300)
      }
    } catch (error) {
      console.error('定位评论失败:', error)
    }
  },

  // 高亮评论
  highlightComment(scrollId) {
    // 可以通过添加CSS类来实现高亮效果
    // 这里暂时使用简单的提示
    wx.showToast({
      title: '已定位到评论',
      icon: 'success',
      duration: 1500
    })
  }
})
