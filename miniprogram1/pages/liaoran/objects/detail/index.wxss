/* pages/liaoran/objects/detail/index.wxss */
/* 完全复制window/detail的样式 */

/* 页面背景 */
.window-detail-bg {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 窗口信息头部 */
.window-header {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.window-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.window-avatar-container {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
  overflow: hidden;
}

.window-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
}

.window-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.window-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.window-tag {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.tag-icon {
  color: #5ec6fa;
  font-size: 28rpx;
  font-weight: 600;
}

.tag-text {
  color: #5ec6fa;
  font-size: 24rpx;
  font-weight: 600;
}

.tag-label {
  color: #5ec6fa;
  font-size: 28rpx;
}

.rating-display {
  text-align: right;
}

.rating-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #5ec6fa;
}

.rating-unit {
  font-size: 28rpx;
  color: #5ec6fa;
  margin-left: 4rpx;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 立即评分区域 */
.rating-section {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.rating-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.rating-note {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.rating-note:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.98);
}

.star-rating {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 0 24rpx;
}

.star-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
}

.star-icon {
  font-size: 36rpx;
  color: #e0e0e0;
  transition: all 0.3s ease;
}

.star-item.active .star-icon {
  color: #5ec6fa;
}

/* 负数评分样式 */
.star-item.active.negative .star-icon {
  color: #ff6b6b; /* 负数评分用红色 */
}

/* 正数评分样式 */
.star-item.active.positive .star-icon {
  color: #5ec6fa; /* 正数评分用蓝色 */
}

.rating-scale {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.scale-item {
  font-size: 24rpx;
  color: #999;
  width: 48rpx;
  text-align: center;
}

/* 全部评论 */
.comments-section {
  background: #fff;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  overflow: hidden;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.sort-tabs {
  display: flex;
  gap: 32rpx;
}

.sort-tab {
  font-size: 28rpx;
  color: #999;
  position: relative;
}

.sort-tab.active {
  color: #5ec6fa;
  font-weight: 600;
}

.sort-tab.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 24rpx;
  height: 4rpx;
  background: #5ec6fa;
  border-radius: 2rpx;
}

/* 评论列表 */
.comments-list {
  padding: 0 32rpx 32rpx;
}

.comment-item {
  display: flex;
  gap: 24rpx;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  margin-bottom: 12rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

/* 评论图片样式 */
.comment-images {
  display: flex;
  gap: 8rpx;
  margin-top: 16rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.comment-image {
  width: calc(33.33% - 6rpx);
  height: 160rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-reply {
  font-size: 24rpx;
  color: #5ec6fa;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 15rpx;
  margin: -10rpx -15rpx;
  border-radius: 20rpx;
}

.comment-actions .action-icon {
  width: 28rpx;
  height: 28rpx;
}

.action-count {
  font-size: 24rpx;
  color: #999;
}

/* 回复列表样式 */
.replies-list {
  margin-top: 24rpx;
  padding-left: 20rpx;
  border-left: 2rpx solid #f0f0f0;
}

.reply-item {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  margin-bottom: 8rpx;
}

.reply-username {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

.reply-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.reply-prefix {
  color: #999;
}

.reply-main-content {
  color: #333;
}

/* 回复图片样式 */
.reply-images {
  display: flex;
  gap: 8rpx;
  margin-top: 12rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.reply-image {
  width: calc(33.33% - 6rpx);
  height: 120rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

.reply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 15rpx;
  margin: -10rpx -15rpx;
  border-radius: 16rpx;
}

.reply-action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.reply-action-count {
  font-size: 22rpx;
  color: #666;
}

/* 无评论样式 */
.no-comment {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  margin: 20rpx 40rpx;
  margin-top: 100rpx;
}

.no-comment-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.no-comment-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 底部遮罩 */
.bottom-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150rpx;
  background: linear-gradient(to top, rgba(248, 249, 250, 1) 0%, rgba(248, 249, 250, 0.8) 50%, rgba(248, 249, 250, 0) 100%);
  z-index: 99;
  pointer-events: none;
}

/* 底部输入框 */
.bottom-fixed-bar {
  position: fixed;
  bottom: 50rpx;
  left: 24rpx;
  right: 24rpx;
  width: auto;
  background-color: #fff;
  padding: 16rpx 24rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-sizing: border-box;
  border-radius: 40rpx;
}

.input-section {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 15rpx 30rpx;
  margin-right: 30rpx;
}

.comment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  margin: -15rpx;
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.action-item:active {
  background-color: transparent !important;
}

.action-icon {
  width: 35rpx;
  height: 35rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 评论遮罩和输入框样式 */
.comment-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
}

.fixed-comment-section {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  z-index: 1001;
  transition: all 0.3s ease;
  border-radius: 20rpx 20rpx 0 0;
  box-shadow: 0 -4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.comment-section-with-keyboard {
  bottom: var(--keyboard-height, 0);
}

.comment-section-with-emoji {
  bottom: 400rpx;
}

.comment-section-with-emoji-list {
  bottom: 600rpx;
}

.image-preview-area {
  display: flex;
  gap: 16rpx;
  padding: 20rpx 24rpx 0;
  flex-wrap: wrap;
}

.image-preview-item {
  position: relative;
  width: 120rpx;
  height: 120rpx;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 12rpx;
}

.remove-image-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 32rpx;
  height: 32rpx;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.quick-emoji-bar {
  display: flex;
  gap: 24rpx;
  padding: 20rpx 24rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.quick-emoji-item {
  padding: 12rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.emoji-text {
  font-size: 32rpx;
}

.input-tools-container {
  padding: 20rpx 24rpx;
}

.comment-textarea {
  width: 100%;
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333;
  border: none;
  outline: none;
  resize: none;
  box-sizing: border-box;
  margin-bottom: 20rpx;
}

.tools-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left-tools {
  display: flex;
  gap: 32rpx;
}

.tool-item {
  position: relative;
  padding: 12rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.tool-icon {
  width: 40rpx;
  height: 40rpx;
}

.image-count {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  background: #ff4757;
  color: #fff;
  border-radius: 50%;
  width: 24rpx;
  height: 24rpx;
  font-size: 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.emoji-icon {
  font-size: 32rpx;
}

.submit-button {
  background: #5ec6fa;
  color: white;
  border: none;
  border-radius: 36rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  margin-right: -12rpx; /* 保留12rpx右边距，看起来更美观 */
  flex-shrink: 0;
}

.submit-button.disabled {
  background: #ccc;
  color: #999;
}

.emoji-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1002;
  border-top: 2rpx solid #f0f0f0;
}

.emoji-scroll {
  height: 100%;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  gap: 16rpx;
}

.emoji-item {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: #f8f9fa;
}

.emoji-list-panel {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 600rpx;
  background: #fff;
  z-index: 1002;
  border-top: 2rpx solid #f0f0f0;
}

.emoji-list-scroll {
  height: 100%;
}

.emoji-list-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  gap: 16rpx;
}

.emoji-list-item {
  width: 80rpx;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 12rpx;
  background: #f8f9fa;
  font-size: 32rpx;
}

/* 文字头像样式 */
.text-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar-text-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 6rpx;
  width: 100%;
  height: 100%;
  padding: 16rpx;
  box-sizing: border-box;
}

.text-char {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 40rpx;
  font-weight: bold;
  line-height: 1;
}