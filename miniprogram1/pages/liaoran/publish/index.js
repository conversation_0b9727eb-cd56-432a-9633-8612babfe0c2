// pages/liaoran/publish/index.js
Page({
  data: {
    categoryName: '',
    imageUrl: '',
    avatarText: '',
    avatarTextArray: ['文','字','头','像'],
    avatarColor: '#A8E6CF', // 随机颜色，默认浅绿色
    avatarMode: 'text', // 默认文字头像模式
    isSubmitting: false,
    canSubmit: false,
    imageUploaded: false, // 添加图片上传状态标志
    colorOptions: ['#A8E6CF', '#FFB3BA', '#FFDFBA', '#BAE1FF', '#FFCCCB']
  },

  onLoad() {
    // 检查用户权限
    this.checkPermissions()
    // 随机设置头像颜色
    this.randomizeAvatarColor()
  },

  // 随机设置头像颜色
  randomizeAvatarColor() {
    const colors = this.data.colorOptions
    const randomIndex = Math.floor(Math.random() * colors.length)
    this.setData({
      avatarColor: colors[randomIndex]
    })
  },

  // 切换到文字头像模式
  switchToTextMode() {
    // 如果图片已上传，不允许切换
    if (this.data.imageUploaded) {
      wx.showToast({
        title: '图片已上传，无法切换',
        icon: 'none'
      })
      return
    }
    this.setData({
      avatarMode: 'text'
    })
  },

  // 切换到图片头像模式
  switchToImageMode() {
    this.setData({
      avatarMode: 'image'
    })
  },

  // 检查是否为汉字
  isChineseChar(char) {
    return /[\u4e00-\u9fa5]/.test(char)
  },

  // 获取汉字数量
  getChineseCharCount(text) {
    if (!text) return 0
    return text.split('').filter(char => this.isChineseChar(char)).length
  },

  // 文字头像输入
  onAvatarTextInput(e) {
    const text = e.detail.value
    const chineseChars = text.split('').filter(char => this.isChineseChar(char))
    const textArray = chineseChars.length > 0 ? chineseChars : ['文','字','头','像']

    this.setData({
      avatarText: text,
      avatarTextArray: textArray
    })
  },

  // 检查用户权限
  checkPermissions() {
    const userStatus = wx.getStorageSync('status')
    if (userStatus === '禁言' || userStatus === 'unverified') {
      wx.showModal({
        title: '权限不足',
        content: '您当前无法发布内容，请联系管理员',
        showCancel: false,
        success: () => {
          wx.navigateBack()
        }
      })
      return
    }
  },

  // 输入分类名称
  onNameInput(e) {
    const value = e.detail.value
    this.setData({
      categoryName: value
    })
    this.checkCanSubmit()
  },



  // 检查是否可以提交
  checkCanSubmit() {
    const canSubmit = this.data.categoryName.trim().length > 0
    this.setData({ canSubmit })
  },

  // 选择图片
  chooseImage() {
    wx.chooseImage({
      count: 1,
      sizeType: ['compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        const tempFilePath = res.tempFilePaths[0]
        // 立即切换到图片模式并显示预览
        this.setData({
          avatarMode: 'image',
          imageUrl: tempFilePath // 先显示本地图片预览
        })
        this.uploadImage(tempFilePath)
      }
    })
  },

  // 上传图片
  uploadImage(filePath) {
    wx.showLoading({ title: '上传中...' })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.hideLoading()
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.uploadFile({
      url: getApp().globalData.wangz + '/upload/uploadCanteenAvatar',
      filePath: filePath,
      name: 'avatar',
      header: {
        'token': token
      },
      success: (res) => {
        wx.hideLoading()
        try {
          const data = JSON.parse(res.data)
          if (data.code === 200) {
            // canteen接口返回完整URL，直接使用
            this.setData({
              imageUrl: data.data.url,
              imageUploaded: true // 设置上传成功标志
            })
            wx.showToast({
              title: '上传成功',
              icon: 'success'
            })
          } else {
            wx.showToast({
              title: data.msg || '上传失败',
              icon: 'none'
            })
          }
        } catch (e) {
          console.error('解析上传响应失败:', e)
          wx.showToast({
            title: '上传失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        wx.hideLoading()
        console.error('上传图片失败:', err)
        wx.showToast({
          title: '上传失败，请重试',
          icon: 'none'
        })
      }
    })
  },



  // 提交分类
  submitCategory() {
    if (!this.data.canSubmit) {
      wx.showToast({
        title: '请输入分类名称',
        icon: 'none'
      })
      return
    }

    if (this.data.isSubmitting) {
      return
    }

    this.setData({ isSubmitting: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      this.setData({ isSubmitting: false })
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 准备提交数据
    const submitData = {
      name: this.data.categoryName.trim()
    }

    // 根据头像模式提交数据
    if (this.data.avatarMode === 'image' && this.data.imageUrl) {
      submitData.image_url = this.data.imageUrl
    } else {
      // 使用文字头像，检查汉字数量
      const avatarText = this.data.avatarText || '文字头像'
      const chineseCharCount = this.getChineseCharCount(avatarText)

      if (chineseCharCount === 0) {
        // 如果没有输入文字，使用默认的"文字头像"
        submitData.avatar_text = '文字头像'
        submitData.avatar_color = this.data.avatarColor
      } else if (chineseCharCount !== 4) {
        wx.showToast({
          title: '请输入4个汉字',
          icon: 'none'
        })
        this.setData({ isSubmitting: false })
        return
      } else {
        // 只保存汉字部分
        const chineseChars = avatarText.split('').filter(char => this.isChineseChar(char)).join('')
        submitData.avatar_text = chineseChars
        submitData.avatar_color = this.data.avatarColor
      }
    }

    wx.request({
      url: getApp().globalData.wangz + '/liaoran/addCategory',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: submitData,
      success: (res) => {
        this.setData({ isSubmitting: false })

        if (res.data.code === 200) {
          wx.showToast({
            title: '发布成功',
            icon: 'success'
          })

          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack()
          }, 1500)
        } else {
          wx.showToast({
            title: res.data.msg || '发布失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        this.setData({ isSubmitting: false })
        console.error('提交失败:', err)
        wx.showToast({
          title: '网络错误，请重试',
          icon: 'none'
        })
      }
    })
  }
})
