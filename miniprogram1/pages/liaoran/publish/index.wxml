<view class="canteen-bg">
  <custom-nav-bar title="发布分类" back="true"></custom-nav-bar>

  <view class="content">
    <view class="form-container">
      <!-- 分类头像 -->
      <view class="form-section">
        <!-- 头像预览 -->
        <view class="avatar-preview">
          <view class="avatar-container text-avatar" style="background-color: {{avatarColor}}">
            <view wx:if="{{avatarMode === 'image' && imageUrl}}" class="avatar-image">
              <image class="avatar-img" src="{{imageUrl}}" mode="aspectFill"></image>
            </view>
            <view wx:else class="avatar-text-grid">
              <view wx:for="{{avatarTextArray}}" wx:key="index" class="text-char">{{item}}</view>
            </view>
          </view>
        </view>

        <!-- 头像模式切换 -->
        <view class="avatar-mode-switch">
          <view class="switch-item {{avatarMode === 'text' ? 'active' : ''}} {{imageUploaded ? 'disabled' : ''}}" bindtap="switchToTextMode">
            <text>文字头像</text>
          </view>
          <view class="switch-item {{avatarMode === 'image' ? 'active' : ''}}" bindtap="switchToImageMode">
            <text>图片头像</text>
          </view>
        </view>

        <!-- 文字头像输入 -->
        <view wx:if="{{avatarMode === 'text'}}" class="form-section">
          <view class="section-title">文字头像（限4个字）</view>
          <view class="input-container">
            <input
              class="name-input"
              placeholder="请输入4个汉字"
              value="{{avatarText}}"
              bindinput="onAvatarTextInput"
            />
            <text class="char-count">{{avatarTextArray.length}}/4</text>
          </view>
        </view>

        <!-- 图片上传按钮 -->
        <view wx:if="{{avatarMode === 'image'}}" class="image-upload-section">
          <button class="upload-btn" bindtap="chooseImage">
            <image src="/images/xiangji.png" class="upload-icon"></image>
            <text>选择图片</text>
          </button>
        </view>
      </view>

      <!-- 分类名称 -->
      <view class="form-section">
        <view class="section-title">分类名称</view>
        <view class="input-container">
          <input
            class="name-input"
            placeholder="请输入分类名称"
            value="{{categoryName}}"
            bindinput="onNameInput"
            maxlength="20"
          />
          <text class="char-count">{{categoryName.length}}/20</text>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <button
        class="submit-btn {{canSubmit && !isSubmitting ? 'active' : 'disabled'}}"
        bindtap="submitCategory"
        disabled="{{!canSubmit || isSubmitting}}"
      >
        {{isSubmitting ? '发布中...' : '发布'}}
      </button>
    </view>
  </view>


</view>
