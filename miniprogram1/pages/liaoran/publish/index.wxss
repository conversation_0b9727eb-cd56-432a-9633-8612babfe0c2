/* pages/liaoran/publish/index.wxss */
/* 引用食堂发布页面的样式 */
@import "/pages/canteen/index.wxss";

/* 描述输入框样式 */
.description-input {
  font-size: 30rpx;
  color: #333;
  width: 100%;
  min-height: 120rpx;
  padding-right: 80rpx;
  resize: none;
}

.description-input::placeholder {
  color: #999;
}

/* 头像相关样式 */
.avatar-preview {
  display: flex;
  justify-content: center;
  margin-bottom: 32rpx;
}

.avatar-container {
  width: 200rpx;
  height: 200rpx;
  border-radius: 20rpx;
  overflow: hidden;
  position: relative;
}

.avatar-img {
  width: 100%;
  height: 100%;
}

.avatar-image {
  width: 100%;
  height: 100%;
  position: absolute;
  top: 0;
  left: 0;
}

.text-avatar {
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #A8E6CF;
}

.avatar-text-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  grid-template-rows: 1fr 1fr;
  gap: 8rpx;
  width: 100%;
  height: 100%;
  padding: 20rpx;
  box-sizing: border-box;
}

.text-char {
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 48rpx;
  font-weight: bold;
  line-height: 1;
}

/* 头像模式切换 */
.avatar-mode-switch {
  display: flex;
  background: #f8f9fa;
  border-radius: 12rpx;
  padding: 8rpx;
  margin: 24rpx 0;
}

.switch-item {
  flex: 1;
  text-align: center;
  padding: 16rpx;
  border-radius: 8rpx;
  font-size: 28rpx;
  color: #666;
  transition: all 0.3s ease;
}

.switch-item.active {
  background: #5ec6fa;
  color: #fff;
  font-weight: 600;
}

.switch-item.disabled {
  background: #f0f0f0;
  color: #ccc;
  cursor: not-allowed;
}



/* 图片上传区域 */
.image-upload-section {
  margin-top: 24rpx;
  text-align: center;
}

.upload-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12rpx;
  padding: 32rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  background: #f8f9fa;
  color: #666;
  font-size: 28rpx;
}

.upload-btn:active {
  background: #e9ecef;
}

.upload-icon {
  width: 48rpx;
  height: 48rpx;
}


