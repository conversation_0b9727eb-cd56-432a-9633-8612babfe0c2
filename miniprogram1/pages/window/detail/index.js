const app = getApp()

// 模拟评论数据
const mockComments = [
  {
    id: 1,
    user: {
      name: '权欲萌妹',
      avatar: '/images/wangzi.png'
    },
    content: '消费他人的苦难并不会显得你高明',
    rating: 1,
    time: '2024-12-16 11:32:14',
    likes: 112,
    replies: []
  },
  {
    id: 2,
    user: {
      name: '小王肚子咕咕叫',
      avatar: '/images/xuesheng.png'
    },
    content: '是的 点了',
    rating: 0,
    time: '2024-12-17 8:43:45',
    likes: 2,
    replies: []
  },
  {
    id: 3,
    user: {
      name: '荒岛',
      avatar: '/images/weixiao.png'
    },
    content: '羊毛月你知道我北大毕业没工作只能做柜员吗 你真的是在站着说话不腰疼😬 毕业即失业，银行笔试面试流程搞了几个月，一看月薪3300，扣除房租1200，水电网350，吃饭1000，交通500=250，就是学了二十二年的结果',
    rating: 2,
    time: '2024-12-16 11:32:11',
    likes: 38,
    replies: []
  },
  {
    id: 4,
    user: {
      name: '小浪花cdlgqz',
      avatar: '/images/beihang.png'
    },
    content: '他干啥了，我居然不知道',
    rating: 0,
    time: '2024-12-16 10:25:33',
    likes: 4,
    replies: []
  }
]

Page({
  data: {
    windowInfo: {},
    comments: [],
    currentRating: 0,
    sortType: 'hot', // hot: 热门, latest: 最新
    isLoading: true,
    totalRating: 0,
    ratingCount: 0,
    showRatingModal: false,
    tempRating: 0,
    commentSectionVisible: false,
    commentInputFocused: false,
    commentContent: '',
    commentPlaceholder: '说点什么...',
    replyToComment: null,
    isSubmittingComment: false,
    keyboardHeight: 0,
    showEmoji: false,
    showEmojiList: false,
    quickEmojiList: ['😊', '😂', '😘', '😍', '😭', '🥰', '😎', '😋'],
    // 图片相关
    selectedImages: [],
    maxImages: 3,
    emojiList: [
      '😀', '😃', '😄', '😁', '😆', '😅', '😂', '🤣', '😊', '😇', '🙂', '🙃', '😉', '😌', '😍', '🥰',
      '😘', '😗', '😙', '😚', '😋', '😛', '😝', '😜', '🤪', '🤨', '🧐', '🤓', '😎', '🤩', '🥳', '😏',
      '😒', '😞', '😔', '😟', '😕', '🙁', '☹️', '😣', '😖', '😫', '😩', '🥺', '😢', '😭', '😤', '😠',
      '😡', '🤬', '🤯', '😳', '🥵', '🥶', '😱', '😨', '😰', '😥', '😓', '🤗', '🤔', '🤭', '🤫', '🤥',
      '😶', '😐', '😑', '😬', '🙄', '😯', '😦', '😧', '😮', '😲', '🥱', '😴', '🤤', '😪', '😵', '🤐'
    ],
    // 推荐菜品相关
    showDishModal: false, // 是否显示推荐菜品弹窗
    dishList: [], // 推荐菜品列表
    newDishName: '', // 新菜品名称
    isSubmittingDish: false // 是否正在提交菜品
  },

  onLoad(options) {
    const windowId = options.id
    const commentId = options.comment_id
    const replyId = options.reply_id

    if (windowId) {
      this.loadWindowDetail(windowId, commentId, replyId)
    }
  },

  loadWindowDetail(windowId, commentId, replyId) {
    this.setData({ isLoading: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ isLoading: false })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/window/getDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: windowId
      },
      success: (res) => {
        if (res.data.code === 200) {
          const windowData = res.data.data

          // 处理窗口头像URL
          let avatarUrl = windowData.avatar || '/images/shitang.png'
          if (avatarUrl.startsWith('/uploads/')) {
            avatarUrl = getApp().globalData.wangz + avatarUrl
          }

          // 处理评论用户头像URL和时间格式化
          const comments = (windowData.comments || []).map(comment => {
            let userAvatar = comment.user.avatar || '/images/default_avatar.png'
            if (userAvatar.startsWith('/uploads/')) {
              userAvatar = getApp().globalData.wangz + userAvatar
            }

            // 处理回复的头像和时间
            const replies = (comment.replies || []).map(reply => {
              let replyAvatar = reply.user.avatar || '/images/default_avatar.png'
              if (replyAvatar.startsWith('/uploads/')) {
                replyAvatar = getApp().globalData.wangz + replyAvatar
              }
              return {
                ...reply,
                user: {
                  ...reply.user,
                  avatar: replyAvatar
                }
              }
            })

            return {
              ...comment,
              user: {
                ...comment.user,
                avatar: userAvatar
              },
              replies: replies
            }
          })

          // 确保评分显示为x.x格式
          const formatRating = (rating) => {
            if (rating === null || rating === undefined || rating === 0) {
              return '0.0'
            }
            return parseFloat(rating).toFixed(1)
          }

          // 处理用户已评分状态
          let currentRating = 0
          if (windowData.userRating !== null && windowData.userRating !== undefined) {
            // 将后端的-10到10转换为前端的1到10
            const userRating = windowData.userRating
            if (userRating <= -2) {
              // 负数评分：-10->1, -8->2, -6->3, -4->4, -2->5
              currentRating = (userRating + 12) / 2
            } else if (userRating >= 2) {
              // 正数评分：2->6, 4->7, 6->8, 8->9, 10->10
              currentRating = (userRating / 2) + 5
            }
          }

          this.setData({
            windowInfo: {
              ...windowData,
              avatar: avatarUrl,
              rating: formatRating(windowData.rating), // 格式化评分
              comments: comments // 将评论数据放到windowInfo中
            },
            totalRating: windowData.rating,
            ratingCount: windowData.ratingCount,
            currentRating: currentRating, // 设置用户当前评分
            isLoading: false
          })

          // 如果有commentId或replyId参数，定位到指定评论
          if (commentId || replyId) {
            setTimeout(() => {
              this.scrollToTargetComment(commentId, replyId, comments)
            }, 500) // 等待页面渲染完成
          }

          // 加载推荐菜品
          this.loadDishRecommendations(windowId)
        } else {
          wx.showToast({
            title: res.data.msg || '获取失败',
            icon: 'none'
          })
          this.setData({ isLoading: false })
        }
      },
      fail: (err) => {
        console.error('获取窗口详情失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        this.setData({ isLoading: false })
      }
    })
  },

  // 切换排序方式
  switchSort(e) {
    const sortType = e.currentTarget.dataset.type
    this.setData({ sortType })

    let sortedComments = [...(this.data.windowInfo.comments || [])]
    if (sortType === 'hot') {
      // 按点赞数排序（降序）
      sortedComments.sort((a, b) => (b.likes || 0) - (a.likes || 0))
    } else {
      // 按时间排序（最新在前）
      sortedComments.sort((a, b) => new Date(b.time) - new Date(a.time))
    }

    this.setData({
      'windowInfo.comments': sortedComments
    })
  },

  // 显示评分弹窗
  showRating() {
    this.setData({
      showRatingModal: true,
      tempRating: 0
    })
  },

  // 隐藏评分弹窗
  hideRating() {
    this.setData({
      showRatingModal: false,
      tempRating: 0
    })
  },

  // 设置评分
  setRating(e) {
    const rating = e.currentTarget.dataset.rating
    // 转换为-10到10的评分范围
    // 1->-10, 2->-8, 3->-6, 4->-4, 5->-2, 6->2, 7->4, 8->6, 9->8, 10->10
    let actualRating
    if (rating <= 5) {
      // 负数评分：1->-10, 2->-8, 3->-6, 4->-4, 5->-2
      actualRating = (rating * 2) - 12
    } else {
      // 正数评分：6->2, 7->4, 8->6, 9->8, 10->10
      actualRating = (rating - 5) * 2
    }



    this.setData({
      tempRating: rating,
      currentRating: rating
    })

    // 立即提交评分
    this.submitRatingDirect(actualRating)
  },

  // 重置评分
  resetRating() {
    this.setData({
      currentRating: 0,
      tempRating: 0
    })
    wx.showToast({
      title: '已重置评分',
      icon: 'success',
      duration: 1500
    })
  },

  // 直接提交评分（仅评分，不包含评论）
  submitRatingDirect(rating) {
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/window/addRating',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        window_id: this.data.windowInfo.id,
        rating: rating
      },
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '评分成功',
            icon: 'success'
          })
          // 通知上级页面需要刷新
          this.notifyParentPageRefresh()
          // 局部更新评分数据，避免页面闪烁
          this.updateRatingData()
        } else {
          wx.showToast({
            title: res.data.msg || '评分失败',
            icon: 'none'
          })
          // 重置评分状态
          this.setData({
            currentRating: 0,
            tempRating: 0
          })
        }
      },
      fail: (err) => {
        console.error('提交评分失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 局部更新评分数据
  updateRatingData() {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    wx.request({
      url: getApp().globalData.wangz + '/window/getDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: this.data.windowInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          const newData = res.data.data
          // 只更新评分相关数据，保持其他数据不变
          this.setData({
            'windowInfo.rating': newData.rating,
            'windowInfo.ratingCount': newData.ratingCount,
            'windowInfo.userRating': newData.userRating
          })
        }
      },
      fail: (err) => {
        console.error('更新评分数据失败:', err)
      }
    })
  },

  // 局部更新评论数据
  updateCommentsData(scrollToNewComment = false) {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    // 获取当前用户ID，用于定位新评论
    const currentUserId = wx.getStorageSync('user_id')

    wx.request({
      url: getApp().globalData.wangz + '/window/getDetail',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        id: this.data.windowInfo.id
      },
      success: (res) => {
        if (res.data.code === 200) {
          const newData = res.data.data

          // 处理评论用户头像URL
          const comments = (newData.comments || []).map(comment => {
            let userAvatar = comment.user.avatar || '/images/default_avatar.png'
            if (userAvatar.startsWith('/uploads/')) {
              userAvatar = getApp().globalData.wangz + userAvatar
            }

            // 处理回复的头像
            const replies = (comment.replies || []).map(reply => {
              let replyAvatar = reply.user.avatar || '/images/default_avatar.png'
              if (replyAvatar.startsWith('/uploads/')) {
                replyAvatar = getApp().globalData.wangz + replyAvatar
              }
              return {
                ...reply,
                user: {
                  ...reply.user,
                  avatar: replyAvatar
                }
              }
            })

            return {
              ...comment,
              user: {
                ...comment.user,
                avatar: userAvatar
              },
              replies: replies
            }
          })

          // 只更新评论相关数据，保持其他数据不变
          this.setData({
            'windowInfo.comments': comments
          })

          // 如果需要滚动到新评论
          if (scrollToNewComment && currentUserId) {
            setTimeout(() => {
              this.scrollToUserLatestComment(currentUserId, comments)
            }, 100) // 等待DOM更新
          }
        }
      },
      fail: (err) => {
        console.error('更新评论数据失败:', err)
      }
    })
  },

  // 滚动到用户最新评论
  scrollToUserLatestComment(userId, comments) {
    try {
      // 查找用户的最新评论（包括回复）
      let targetCommentIndex = -1
      let targetReplyIndex = -1
      let latestTime = 0

      comments.forEach((comment, commentIndex) => {
        // 检查主评论
        if (comment.user.id == userId) {
          const commentTime = new Date(comment.time).getTime()
          if (commentTime > latestTime) {
            latestTime = commentTime
            targetCommentIndex = commentIndex
            targetReplyIndex = -1
          }
        }

        // 检查回复
        if (comment.replies && comment.replies.length > 0) {
          comment.replies.forEach((reply, replyIndex) => {
            if (reply.user.id == userId) {
              const replyTime = new Date(reply.time).getTime()
              if (replyTime > latestTime) {
                latestTime = replyTime
                targetCommentIndex = commentIndex
                targetReplyIndex = replyIndex
              }
            }
          })
        }
      })

      // 如果找到了用户的评论，滚动到该位置
      if (targetCommentIndex >= 0) {
        let scrollId
        if (targetReplyIndex >= 0) {
          // 滚动到回复
          scrollId = `reply-${targetCommentIndex}-${targetReplyIndex}`
        } else {
          // 滚动到主评论
          scrollId = `comment-${targetCommentIndex}`
        }

        wx.pageScrollTo({
          selector: `#${scrollId}`,
          duration: 300
        })
      }
    } catch (error) {
      console.error('滚动到评论失败:', error)
    }
  },

  // 立即添加新评论到本地数据
  addNewCommentToLocal(commentData, requestData) {
    try {
      const currentUserId = wx.getStorageSync('user_id')
      const currentUsername = wx.getStorageSync('username')
      const currentUserAvatar = wx.getStorageSync('face_url')

      // 构造新评论对象
      const newComment = {
        id: commentData.id || Date.now(), // 使用服务器返回的ID或临时ID
        content: requestData.content,
        time: new Date().toISOString(),
        likes: 0,
        is_liked: false,
        user: {
          id: currentUserId,
          name: currentUsername || '匿名用户',
          avatar: currentUserAvatar ? (currentUserAvatar.startsWith('/uploads/') ?
            getApp().globalData.wangz + currentUserAvatar : currentUserAvatar) :
            '/images/default_avatar.png'
        },
        replies: []
      }

      // 如果有图片，添加图片
      if (requestData.images) {
        try {
          newComment.images = JSON.parse(requestData.images)
        } catch (e) {
          console.error('解析图片数据失败:', e)
          newComment.images = []
        }
      }

      const comments = [...(this.data.windowInfo.comments || [])]

      // 如果是回复评论
      if (requestData.parent_id) {
        // 找到父评论
        const parentIndex = comments.findIndex(comment => comment.id == requestData.parent_id)
        if (parentIndex >= 0) {
          // 构造回复对象
          const newReply = {
            id: commentData.id || Date.now(),
            content: requestData.content,
            time: new Date().toISOString(),
            likes: 0,
            is_liked: false,
            user: {
              id: currentUserId,
              name: currentUsername || '匿名用户',
              avatar: currentUserAvatar ? (currentUserAvatar.startsWith('/uploads/') ?
                getApp().globalData.wangz + currentUserAvatar : currentUserAvatar) :
                '/images/default_avatar.png'
            }
          }

          // 如果有图片，添加图片
          if (requestData.images) {
            try {
              newReply.images = JSON.parse(requestData.images)
            } catch (e) {
              console.error('解析回复图片数据失败:', e)
              newReply.images = []
            }
          }

          // 如果是回复特定用户，添加回复目标信息
          if (requestData.reply_to_user_id) {
            // 找到被回复的用户名
            const replyToUser = this.findUserInComments(comments[parentIndex], requestData.reply_to_user_id)
            if (replyToUser) {
              newReply.reply_to_username = replyToUser.name
            }
          }

          // 添加回复到父评论
          if (!comments[parentIndex].replies) {
            comments[parentIndex].replies = []
          }
          comments[parentIndex].replies.push(newReply)
        }
      } else {
        // 添加新的主评论到列表开头
        comments.unshift(newComment)
      }

      // 更新本地数据
      this.setData({
        'windowInfo.comments': comments
      })

      // 立即滚动到新评论
      setTimeout(() => {
        if (requestData.parent_id) {
          // 回复评论，滚动到回复
          const parentIndex = comments.findIndex(comment => comment.id == requestData.parent_id)
          if (parentIndex >= 0 && comments[parentIndex].replies) {
            const replyIndex = comments[parentIndex].replies.length - 1
            const scrollId = `reply-${parentIndex}-${replyIndex}`
            wx.pageScrollTo({
              selector: `#${scrollId}`,
              duration: 300
            })
          }
        } else {
          // 主评论，滚动到第一个评论
          wx.pageScrollTo({
            selector: '#comment-0',
            duration: 300
          })
        }
      }, 100)

    } catch (error) {
      console.error('添加新评论到本地失败:', error)
    }
  },

  // 在评论中查找用户
  findUserInComments(comment, userId) {
    // 检查主评论用户
    if (comment.user.id == userId) {
      return comment.user
    }

    // 检查回复中的用户
    if (comment.replies && comment.replies.length > 0) {
      for (const reply of comment.replies) {
        if (reply.user.id == userId) {
          return reply.user
        }
      }
    }

    return null
  },

  // 处理评论点击
  handleCommentClick() {
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: '说点什么...',
      commentContent: '',
      replyToComment: null,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 回复评论
  replyComment(e) {
    const comment = e.currentTarget.dataset.comment
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${comment.user.name}`,
      commentContent: '',
      replyToComment: comment,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 点击评论内容区域回复
  clickCommentToReply(e) {
    const comment = e.currentTarget.dataset.comment
    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${comment.user.name}`,
      commentContent: '',
      replyToComment: comment,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 点击回复内容区域回复
  clickReplyToReply(e) {
    const reply = e.currentTarget.dataset.reply
    const commentIndex = e.currentTarget.dataset.commentIndex

    // 构造回复对象，包含父评论信息
    const replyToComment = {
      id: reply.id,
      user: reply.user,
      parent_id: this.data.windowInfo.comments[commentIndex].id // 父评论ID
    }

    this.setData({
      commentSectionVisible: true,
      commentInputFocused: true,
      commentPlaceholder: `回复 ${reply.user.name}`,
      commentContent: '',
      replyToComment: replyToComment,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [] // 重置图片
    })
  },

  // 输入评论内容
  onCommentInput(e) {
    this.setData({
      commentContent: e.detail.value
    })
  },

  // 处理输入框失去焦点
  handleInputBlur() {
    setTimeout(() => {
      // 只有当表情面板都没显示时，才重置状态
      if (!this.data.showEmoji && !this.data.showEmojiList) {
        this.setData({
          commentInputFocused: false,
          keyboardHeight: 0
        })
      }
    }, 100)
  },

  // 处理输入框获取焦点
  handleInputFocus() {
    this.setData({
      showEmoji: false,
      showEmojiList: false,
      commentInputFocused: true,
      commentSectionVisible: true
    })
  },

  // 处理键盘高度变化
  handleKeyboardHeightChange(e) {
    this.setData({
      keyboardHeight: e.detail.height
    })
  },

  // 隐藏评论区域
  hideCommentSection() {
    this.setData({
      commentSectionVisible: false,
      commentInputFocused: false,
      commentContent: '',
      replyToComment: null,
      keyboardHeight: 0,
      showEmoji: false,
      showEmojiList: false,
      selectedImages: [], // 重置图片
      isSubmittingComment: false // 重置发送状态
    })
  },

  // 选择图片
  chooseImage() {
    const remainingCount = this.data.maxImages - this.data.selectedImages.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多只能选择${this.data.maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: remainingCount,
      mediaType: ['image'],
      sourceType: ['album'],
      maxDuration: 30,
      camera: 'back',
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath)
        this.setData({
          selectedImages: [...this.data.selectedImages, ...tempFiles]
        })
      },
      fail: (err) => {
        console.error('选择图片失败:', err)
        wx.showToast({
          title: '选择图片失败',
          icon: 'none'
        })
      }
    })
  },

  // 打开相机
  openCamera() {
    const remainingCount = this.data.maxImages - this.data.selectedImages.length
    if (remainingCount <= 0) {
      wx.showToast({
        title: `最多只能选择${this.data.maxImages}张图片`,
        icon: 'none'
      })
      return
    }

    wx.chooseMedia({
      count: 1,
      mediaType: ['image'],
      sourceType: ['camera'],
      camera: 'back',
      success: (res) => {
        const tempFiles = res.tempFiles.map(file => file.tempFilePath)
        this.setData({
          selectedImages: [...this.data.selectedImages, ...tempFiles]
        })
      },
      fail: (err) => {
        console.error('拍照失败:', err)
        wx.showToast({
          title: '拍照失败',
          icon: 'none'
        })
      }
    })
  },

  // 删除图片
  removeImage(e) {
    const index = e.currentTarget.dataset.index
    const selectedImages = [...this.data.selectedImages]
    selectedImages.splice(index, 1)
    this.setData({
      selectedImages
    })
  },

  // 预览图片
  previewImage(e) {
    const index = e.currentTarget.dataset.index
    wx.previewImage({
      current: this.data.selectedImages[index],
      urls: this.data.selectedImages
    })
  },

  // 提交评论
  submitComment() {
    if (!this.data.commentContent.trim() && this.data.selectedImages.length === 0) {
      wx.showToast({
        title: '请输入评论内容或选择图片',
        icon: 'none'
      })
      return
    }

    if (this.data.isSubmittingComment) {
      return
    }

    this.setData({ isSubmittingComment: true })

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      this.setData({ isSubmittingComment: false })
      return
    }

    // 如果有图片，先上传图片
    if (this.data.selectedImages.length > 0) {
      this.uploadImagesAndSubmit()
    } else {
      this.submitCommentWithData({})
    }
  },

  // 上传图片并提交评论
  uploadImagesAndSubmit() {
    const token = wx.getStorageSync('access_token')
    const uploadPromises = this.data.selectedImages.map((imagePath, index) => {
      return new Promise((resolve) => {  // 改为总是resolve，不reject
        wx.uploadFile({
          url: getApp().globalData.wangz + '/upload/uploadImage',  // 使用正确的接口路径
          filePath: imagePath,
          name: 'file',
          formData: {
            user_id: getApp().globalData.user_id  // 添加用户ID
          },
          header: {
            'token': token
          },
          success: (res) => {
            console.log(`图片${index + 1}上传响应:`, res.data)  // 添加日志
            try {
              const data = JSON.parse(res.data)
              // 兼容多种返回格式
              if (data.error_code === 0) {
                // 格式1: {error_code: 0, data: {urls: [...]}}
                if (data.data && data.data.urls && data.data.urls.length > 0) {
                  resolve({ success: true, url: data.data.urls[0] })
                }
                // 格式2: {error_code: 0, image_url: "..."}
                else if (data.image_url) {
                  resolve({ success: true, url: data.image_url })
                }
                // 格式3: {error_code: 0, data: {image_url: "..."}}
                else if (data.data && data.data.image_url) {
                  resolve({ success: true, url: data.data.image_url })
                }
                else {
                  resolve({ success: false, error: '返回数据格式错误' })
                }
              }
              // 格式4: {code: 200, data: {url: "..."}}
              else if (data.code === 200) {
                if (data.data && data.data.url) {
                  resolve({ success: true, url: data.data.url })
                } else {
                  resolve({ success: false, error: '返回数据格式错误' })
                }
              }
              else {
                resolve({ success: false, error: data.msg || data.message || '上传失败' })
              }
            } catch (e) {
              console.error('JSON解析失败:', e, '原始数据:', res.data)
              resolve({ success: false, error: '解析响应失败: ' + e.message })
            }
          },
          fail: (err) => {
            console.error(`图片${index + 1}上传请求失败:`, err)
            resolve({ success: false, error: err.errMsg || '上传失败' })
          }
        })
      })
    })

    Promise.all(uploadPromises)
      .then(results => {
        console.log('图片上传结果:', results)

        // 筛选出成功上传的图片
        const successfulUploads = results.filter(result => result.success)
        const failedUploads = results.filter(result => !result.success)

        if (failedUploads.length > 0) {
          console.warn('部分图片上传失败:', failedUploads)
          wx.showToast({
            title: `${failedUploads.length}张图片上传失败`,
            icon: 'none',
            duration: 2000
          })
        }

        if (successfulUploads.length > 0) {
          // 有成功上传的图片，继续提交评论
          const imageUrls = successfulUploads.map(result => result.url)
          console.log('成功上传的图片:', imageUrls)
          this.submitCommentWithData({ images: JSON.stringify(imageUrls) })
        } else {
          // 所有图片都上传失败
          wx.showToast({
            title: '所有图片上传失败',
            icon: 'none'
          })
          this.setData({ isSubmittingComment: false })
        }
      })
  },

  // 提交评论数据
  submitCommentWithData(extraData = {}) {
    const token = wx.getStorageSync('access_token')
    const data = {
      window_id: this.data.windowInfo.id,
      content: this.data.commentContent.trim(),
      ...extraData
    }

    // 如果是回复评论
    if (this.data.replyToComment) {
      if (this.data.replyToComment.parent_id) {
        // 回复的回复，使用父评论ID，并设置reply_to_user_id
        data.parent_id = this.data.replyToComment.parent_id
        // 确保user_id存在且为有效数字
        const replyUserId = this.data.replyToComment.user && this.data.replyToComment.user.id
        data.reply_to_user_id = replyUserId && !isNaN(replyUserId) ? replyUserId : null
      } else {
        // 直接回复评论，不设置reply_to_user_id
        data.parent_id = this.data.replyToComment.id
        data.reply_to_user_id = null
      }
    }

    wx.request({
      url: getApp().globalData.wangz + '/window/addComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: data,
      success: (res) => {
        console.log('评论响应:', res.data)
        if (res.data.code === 200) {
          wx.showToast({
            title: '评论成功',
            icon: 'success'
          })

          // 立即添加新评论到本地数据，避免等待服务器响应
          this.addNewCommentToLocal(res.data.data, data)

          this.hideCommentSection()

          // 通知上级页面需要刷新
          this.notifyParentPageRefresh()

          // 延迟更新完整数据，确保服务器数据同步
          setTimeout(() => {
            this.updateCommentsData(true)
          }, 500)
        } else {
          wx.showToast({
            title: res.data.msg || '评论失败',
            icon: 'none'
          })
          // 发送失败时重置按钮状态
          this.setData({ isSubmittingComment: false })
        }
      },
      fail: (err) => {
        console.error('提交评论失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
        // 发送失败时重置按钮状态
        this.setData({ isSubmittingComment: false })
      },
      complete: () => {
        // 成功时在hideCommentSection中重置，失败时在上面重置
      }
    })
  },



  // 切换表情面板
  toggleEmoji() {
    if (this.data.showEmoji) {
      this.setData({
        showEmoji: false,
        commentInputFocused: true
      })
    } else {
      this.setData({
        showEmoji: true,
        showEmojiList: false,
        commentInputFocused: false,
        keyboardHeight: 0
      })
    }
  },

  // 切换emoji表情列表
  toggleEmojiList() {
    if (this.data.showEmojiList) {
      this.setData({
        showEmojiList: false,
        commentInputFocused: true
      })
    } else {
      this.setData({
        showEmojiList: true,
        showEmoji: false,
        commentInputFocused: false,
        keyboardHeight: 0
      })
    }
  },

  // 选择表情
  selectEmoji(e) {
    const emoji = e.currentTarget.dataset.emoji
    const content = this.data.commentContent + emoji
    this.setData({
      commentContent: content
    })
  },

  // 点赞评论
  likeComment(e) {
    const commentId = e.currentTarget.dataset.id
    const commentIndex = e.currentTarget.dataset.index

    if (!commentId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/window/likeComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        comment_id: commentId
      },
      success: (res) => {
        console.log('点赞响应:', res.data)
        if (res.data.code === 200) {
          // 更新本地评论的点赞状态
          const comments = [...this.data.windowInfo.comments]
          if (comments[commentIndex]) {
            comments[commentIndex].is_liked = res.data.data.is_liked
            comments[commentIndex].likes = res.data.data.total_likes
          }

          this.setData({
            'windowInfo.comments': comments
          })

          // 显示提示
          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('点赞失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 点赞回复
  likeReply(e) {
    const replyId = e.currentTarget.dataset.replyId
    const commentIndex = e.currentTarget.dataset.commentIndex
    const replyIndex = e.currentTarget.dataset.replyIndex

    if (!replyId) {
      wx.showToast({
        title: '参数错误',
        icon: 'none'
      })
      return
    }

    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    wx.request({
      url: getApp().globalData.wangz + '/window/likeComment',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        comment_id: replyId
      },
      success: (res) => {
        console.log('回复点赞响应:', res.data)
        if (res.data.code === 200) {
          // 更新本地回复的点赞状态
          const comments = [...this.data.windowInfo.comments]
          if (comments[commentIndex] && comments[commentIndex].replies && comments[commentIndex].replies[replyIndex]) {
            comments[commentIndex].replies[replyIndex].is_liked = res.data.data.is_liked
            comments[commentIndex].replies[replyIndex].likes = res.data.data.total_likes
          }

          this.setData({
            'windowInfo.comments': comments
          })

          // 显示提示
          wx.showToast({
            title: res.data.data.is_liked ? '点赞成功(*´∀`)~♥' : '取消点赞(⁰﹏⁰)',
            icon: 'none',
            duration: 750
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('回复点赞失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 点赞窗口
  likeWindow() {
    wx.showToast({
      title: '点赞功能开发中',
      icon: 'none'
    })
  },



  // 预览评论图片
  previewCommentImage(e) {
    const url = e.currentTarget.dataset.url
    const images = e.currentTarget.dataset.images
    wx.previewImage({
      current: url,
      urls: images
    })
  },

  // 预览窗口头像
  previewWindowAvatar(e) {
    const imageUrl = e.currentTarget.dataset.img
    if (imageUrl) {
      wx.previewImage({
        current: imageUrl,
        urls: [imageUrl]
      })
    }
  },

  // 返回上一页
  goBack() {
    wx.navigateBack()
  },

  // 通知上级页面需要刷新
  notifyParentPageRefresh() {
    try {
      const pages = getCurrentPages()
      if (pages.length >= 2) {
        const prevPage = pages[pages.length - 2]
        // 检查上级页面是否是食堂详情页
        if (prevPage.route === 'pages/canteen/detail/index' && typeof prevPage.setNeedRefresh === 'function') {
          prevPage.setNeedRefresh()
        }
      }
    } catch (error) {
      console.error('通知上级页面刷新失败:', error)
    }
  },

  // 加载推荐菜品列表
  loadDishRecommendations(windowId) {
    const token = wx.getStorageSync('access_token')
    if (!token) return

    wx.request({
      url: getApp().globalData.wangz + '/window/getDishRecommendations',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        window_id: windowId
      },
      success: (res) => {
        if (res.data.code === 200) {
          this.setData({
            dishList: res.data.data || []
          })
        } else {
          console.error('获取推荐菜品失败:', res.data.msg)
        }
      },
      fail: (err) => {
        console.error('获取推荐菜品网络错误:', err)
      }
    })
  },

  // 显示推荐菜品弹窗
  showDishRecommendModal() {
    const token = wx.getStorageSync('access_token')
    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    this.setData({
      showDishModal: true,
      newDishName: ''
    })

    // 重新加载推荐菜品数据
    this.loadDishRecommendations(this.data.windowInfo.id)
  },

  // 隐藏推荐菜品弹窗
  hideDishRecommendModal() {
    this.setData({
      showDishModal: false,
      newDishName: ''
    })
  },

  // 阻止事件冒泡
  stopPropagation() {
    // 空函数，用于阻止事件冒泡
  },

  // 切换菜品推荐状态
  toggleDishRecommend(e) {
    const dishName = e.currentTarget.dataset.dish
    const token = wx.getStorageSync('access_token')

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    // 找到当前菜品
    const dishList = [...this.data.dishList]
    const dishIndex = dishList.findIndex(item => item.dish_name === dishName)

    if (dishIndex === -1) return

    const dish = dishList[dishIndex]
    const action = dish.isRecommended ? 'remove' : 'add'

    wx.request({
      url: getApp().globalData.wangz + '/window/toggleDishRecommendation',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        window_id: this.data.windowInfo.id,
        dish_name: dishName,
        action: action
      },
      success: (res) => {
        if (res.data.code === 200) {
          // 更新本地数据
          if (action === 'add') {
            dish.isRecommended = true
            dish.count += 1
            wx.showToast({
              title: '推荐成功',
              icon: 'success'
            })
          } else {
            dish.isRecommended = false
            dish.count = Math.max(0, dish.count - 1)
            wx.showToast({
              title: '取消推荐成功',
              icon: 'success'
            })
          }

          dishList[dishIndex] = dish
          this.setData({
            dishList: dishList
          })
        } else {
          wx.showToast({
            title: res.data.msg || '操作失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('推荐菜品操作失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      }
    })
  },

  // 输入菜品名称
  onDishNameInput(e) {
    this.setData({
      newDishName: e.detail.value.trim()
    })
  },

  // 添加新菜品
  addNewDish() {
    const dishName = this.data.newDishName.trim()
    const token = wx.getStorageSync('access_token')

    if (!token) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      })
      return
    }

    if (!dishName) {
      wx.showToast({
        title: '请输入菜品名称',
        icon: 'none'
      })
      return
    }

    if (dishName.length > 10) {
      wx.showToast({
        title: '菜品名称不能超过10个字符',
        icon: 'none'
      })
      return
    }

    // 检查是否已存在
    const existingDish = this.data.dishList.find(item => item.dish_name === dishName)
    if (existingDish) {
      wx.showToast({
        title: '该菜品已存在',
        icon: 'none'
      })
      return
    }

    if (this.data.isSubmittingDish) return

    this.setData({ isSubmittingDish: true })

    wx.request({
      url: getApp().globalData.wangz + '/window/toggleDishRecommendation',
      method: 'POST',
      header: {
        'content-type': 'application/x-www-form-urlencoded',
        'token': token
      },
      data: {
        window_id: this.data.windowInfo.id,
        dish_name: dishName,
        action: 'add'
      },
      success: (res) => {
        if (res.data.code === 200) {
          wx.showToast({
            title: '添加成功',
            icon: 'success'
          })

          // 添加到本地列表
          const dishList = [...this.data.dishList]
          dishList.push({
            dish_name: dishName,
            count: 1,
            isRecommended: true
          })

          this.setData({
            dishList: dishList,
            newDishName: ''
          })
        } else {
          wx.showToast({
            title: res.data.msg || '添加失败',
            icon: 'none'
          })
        }
      },
      fail: (err) => {
        console.error('添加菜品失败:', err)
        wx.showToast({
          title: '网络错误',
          icon: 'none'
        })
      },
      complete: () => {
        this.setData({ isSubmittingDish: false })
      }
    })
  },

  // 定位到指定评论
  scrollToTargetComment(commentId, replyId, comments) {
    try {
      let targetCommentIndex = -1
      let targetReplyIndex = -1

      // 查找目标评论或回复
      comments.forEach((comment, commentIndex) => {
        if (replyId) {
          // 查找回复
          if (comment.replies && comment.replies.length > 0) {
            comment.replies.forEach((reply, replyIndex) => {
              if (reply.id == replyId) {
                targetCommentIndex = commentIndex
                targetReplyIndex = replyIndex
              }
            })
          }
        } else if (commentId) {
          // 查找评论
          if (comment.id == commentId) {
            targetCommentIndex = commentIndex
          }
        }
      })

      // 如果找到了目标，滚动到该位置
      if (targetCommentIndex >= 0) {
        let scrollId
        if (targetReplyIndex >= 0) {
          // 滚动到回复
          scrollId = `reply-${targetCommentIndex}-${targetReplyIndex}`
        } else {
          // 滚动到主评论
          scrollId = `comment-${targetCommentIndex}`
        }

        wx.pageScrollTo({
          selector: `#${scrollId}`,
          duration: 300
        })

        // 添加高亮效果
        setTimeout(() => {
          this.highlightComment(scrollId)
        }, 300)
      }
    } catch (error) {
      console.error('定位评论失败:', error)
    }
  },

  // 高亮评论
  highlightComment(scrollId) {
    // 可以通过添加CSS类来实现高亮效果
    // 这里暂时使用简单的提示
    wx.showToast({
      title: '已定位到评论',
      icon: 'success',
      duration: 1500
    })
  }

})
