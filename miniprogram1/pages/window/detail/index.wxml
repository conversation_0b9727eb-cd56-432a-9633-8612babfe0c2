<wxs module="timeUtils">
  function formatTime(timeStr) {
    if (!timeStr) return ''

    // 处理iOS日期格式兼容性问题
    // 将 "2025-05-26 12:25:43" 格式转换为 "2025/05/26 12:25:43"
    // WXS不支持正则表达式，使用split和join方法替换
    var parts = timeStr.split('-')
    var formattedTimeStr = parts.join('/')

    var date = getDate(formattedTimeStr)
    var now = getDate()

    // 计算时间差（毫秒）
    var diffMs = now.getTime() - date.getTime()
    var diffHours = Math.floor(diffMs / (1000 * 60 * 60))
    var diffDays = Math.floor(diffMs / (1000 * 60 * 60 * 24))

    // 一天内显示x小时前
    if (diffHours < 24 && diffDays === 0) {
      if (diffHours <= 0) {
        return '刚刚'
      }
      return diffHours + '小时前'
    }
    // 3天内显示x天前
    else if (diffDays <= 3) {
      return diffDays + '天前'
    }
    // 超过3天显示具体日期 YY-MM-DD 格式（如：25-05-26）
    else {
      var year = (date.getFullYear() + '').slice(-2) // 取年份后两位
      var month = (date.getMonth() + 1 + '').length === 1 ? '0' + (date.getMonth() + 1) : (date.getMonth() + 1 + '')
      var day = (date.getDate() + '').length === 1 ? '0' + date.getDate() : (date.getDate() + '')

      return year + '-' + month + '-' + day
    }
  }

  function getTotalCommentCount(comments) {
    if (!comments || comments.length === 0) return 0

    var total = comments.length // 主评论数

    // 加上所有回复数
    for (var i = 0; i < comments.length; i++) {
      if (comments[i].replies && comments[i].replies.length > 0) {
        total += comments[i].replies.length
      }
    }

    return total
  }

  module.exports = {
    formatTime: formatTime,
    getTotalCommentCount: getTotalCommentCount
  }
</wxs>

<view class="window-detail-bg">
  <custom-nav-bar title="评分详情" back="true"></custom-nav-bar>
  <loading show="{{isLoading}}" mask="false"></loading>

  <view wx:if="{{!isLoading}}">
    <!-- 窗口信息头部 -->
    <view class="window-header">
      <view class="window-info">
        <image class="window-avatar" src="{{windowInfo.avatar}}" mode="aspectFill" catchtap="previewWindowAvatar" data-img="{{windowInfo.avatar}}"></image>
        <view class="window-details">
          <view class="window-name">{{windowInfo.name}}</view>
          <view class="window-tag">
            <text class="tag-icon">"</text>
            <text class="tag-text">{{windowInfo.tag}}</text>
            <text class="tag-icon">"</text>
            <text class="tag-label">创建</text>
          </view>
        </view>
      </view>
      <view class="rating-display">
        <text class="rating-number">{{windowInfo.rating}}</text>
        <text class="rating-unit">分</text>
        <view class="rating-count">{{windowInfo.ratingCount}}人评分</view>
      </view>
    </view>

    <!-- 立即评分区域 -->
    <view class="rating-section">
      <view class="rating-header">
        <text class="rating-title">立即评分</text>
        <text class="rating-note" bindtap="resetRating">点击可以再次评分</text>
      </view>
      <view class="star-rating">
        <view class="star-item {{(currentRating > 0 && currentRating <= 5 && index + 1 >= currentRating && index + 1 <= 5) ? 'active negative' : ((currentRating > 5 && index + 1 >= 6 && index + 1 <= currentRating) ? 'active positive' : '')}}"
              wx:for="{{10}}" wx:key="*this"
              bindtap="setRating" data-rating="{{index + 1}}">
          <text class="star-icon">★</text>
        </view>
      </view>
      <view class="rating-scale">
        <text class="scale-item">-10</text>
        <text class="scale-item">-8</text>
        <text class="scale-item">-6</text>
        <text class="scale-item">-4</text>
        <text class="scale-item">-2</text>
        <text class="scale-item">2</text>
        <text class="scale-item">4</text>
        <text class="scale-item">6</text>
        <text class="scale-item">8</text>
        <text class="scale-item">10</text>
      </view>

    </view>

    <!-- 推荐菜品区域 -->
    <view class="dish-recommendation-section">
      <view class="dish-recommendation-header">
        <text class="dish-recommendation-title">推荐菜品</text>
        <view class="recommend-dish-btn" bindtap="showDishRecommendModal">
          <text class="recommend-btn-text">点击此处添加新菜</text>
        </view>
      </view>

      <!-- 推荐菜品列表 -->
      <view class="dish-recommendation-list" wx:if="{{dishList && dishList.length > 0}}">
        <view class="dish-recommendation-item {{item.isRecommended ? 'recommended' : 'not-recommended'}}"
              wx:for="{{dishList}}" wx:key="dish_name"
              bindtap="toggleDishRecommend" data-dish="{{item.dish_name}}">
          <text class="dish-name">{{item.dish_name}}</text>
          <text class="dish-count">{{item.count}}</text>
        </view>
      </view>

      <!-- 无推荐菜品时显示 -->
      <view wx:if="{{!dishList || dishList.length === 0}}" class="no-dish-recommendations">
        <text class="no-dish-recommendations-text">暂无推荐菜品</text>
      </view>
    </view>

    <!-- 全部评论 -->
    <view class="comments-section">
      <view class="comments-header">
        <text class="comments-title">全部评论</text>
        <view class="sort-tabs">
          <text class="sort-tab {{sortType === 'hot' ? 'active' : ''}}"
                bindtap="switchSort" data-type="hot">热门</text>
          <text class="sort-tab {{sortType === 'latest' ? 'active' : ''}}"
                bindtap="switchSort" data-type="latest">最新</text>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comments-list" wx:if="{{windowInfo.comments && windowInfo.comments.length > 0}}">
        <view class="comment-item" wx:for="{{windowInfo.comments}}" wx:key="id" id="comment-{{index}}">
          <image class="comment-avatar" src="{{item.user.avatar}}" mode="aspectFill" bindtap="clickCommentToReply" data-comment="{{item}}"></image>
          <view class="comment-content" bindtap="clickCommentToReply" data-comment="{{item}}">
            <view class="comment-header" bindtap="clickCommentToReply" data-comment="{{item}}">
              <text class="comment-username">{{item.user.name}}</text>
            </view>
            <view class="comment-text" bindtap="clickCommentToReply" data-comment="{{item}}">{{item.content}}</view>
            <!-- 评论图片显示 -->
            <view wx:if="{{item.images && item.images.length > 0}}" class="comment-images">
              <image wx:for="{{item.images}}" wx:key="index" wx:for-item="img"
                     src="{{img}}" mode="aspectFill" class="comment-image"
                     catchtap="previewCommentImage"
                     data-url="{{img}}"
                     data-images="{{item.images}}">
              </image>
            </view>
            <view class="comment-footer">
              <text class="comment-time" bindtap="clickCommentToReply" data-comment="{{item}}">{{timeUtils.formatTime(item.time)}}</text>
              <text class="comment-reply" bindtap="replyComment" data-comment="{{item}}">回复</text>
              <view class="comment-actions" catchtap="likeComment" data-id="{{item.id}}" data-index="{{index}}">
                <image src="{{item.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" class="action-icon"></image>
                <text class="action-count">{{item.likes}}</text>
              </view>
            </view>
            <!-- 回复列表 -->
            <view class="replies-list" wx:if="{{item.replies && item.replies.length > 0}}">
              <view class="reply-item" wx:for="{{item.replies}}" wx:key="id" wx:for-item="reply" wx:for-index="replyIndex" id="reply-{{index}}-{{replyIndex}}">
                <image class="reply-avatar" src="{{reply.user.avatar}}" mode="aspectFill" bindtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}"></image>
                <view class="reply-content" bindtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}">
                  <view class="reply-header" bindtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}">
                    <text class="reply-username">{{reply.user.name}}</text>
                  </view>
                  <view class="reply-text" bindtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}">
                    <text wx:if="{{reply.reply_to_username && reply.reply_to_username !== ''}}" class="reply-prefix">回复 {{reply.reply_to_username}}: </text>
                    <text class="reply-main-content">{{reply.content}}</text>
                  </view>
                  <!-- 回复图片显示 -->
                  <view wx:if="{{reply.images && reply.images.length > 0}}" class="reply-images">
                    <image wx:for="{{reply.images}}" wx:key="index" wx:for-item="img"
                           src="{{img}}" mode="aspectFill" class="reply-image"
                           catchtap="previewCommentImage"
                           data-url="{{img}}"
                           data-images="{{reply.images}}">
                    </image>
                  </view>
                  <view class="reply-footer">
                    <text class="reply-time" bindtap="clickReplyToReply" data-reply="{{reply}}" data-comment-index="{{index}}">{{timeUtils.formatTime(reply.time)}}</text>
                    <view class="reply-actions" catchtap="likeReply" data-reply-id="{{reply.id}}" data-comment-index="{{index}}" data-reply-index="{{replyIndex}}">
                      <image src="{{reply.is_liked ? '/images/icon-2.png' : '/images/icon.png'}}" class="reply-action-icon"></image>
                      <text class="reply-action-count">{{reply.likes}}</text>
                    </view>
                  </view>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 无评论时显示 -->
      <view wx:if="{{!isLoading && (!windowInfo.comments || windowInfo.comments.length === 0)}}" class="no-comment">
        <image src="https://www.bjgaoxiaoshequ.store/images/content.png" mode="aspectFit" class="no-comment-image"></image>
        <text class="no-comment-text">一片荒原，来说点什么吧～</text>
      </view>
    </view>

    <!-- 底部遮罩 -->
    <view class="bottom-mask"></view>

    <!-- 底部输入框 -->
    <view class="bottom-fixed-bar">
      <view class="input-section" bindtap="handleCommentClick">
        <image src="/images/dangshidati-01.png" class="comment-icon" />
        <text class="input-placeholder">说点什么...</text>
      </view>
      <view class="action-section">
        <view class="action-item" bindtap="handleCommentClick">
          <image src="/images/pinglun2.png" class="action-icon" />
          <text class="action-text">{{timeUtils.getTotalCommentCount(windowInfo.comments)}}</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 评论遮罩层 -->
  <view wx:if="{{commentSectionVisible}}" class="comment-mask" bindtap="hideCommentSection"></view>

  <!-- 评论输入框 -->
  <view wx:if="{{commentSectionVisible}}"
        class="fixed-comment-section {{showEmoji ? 'comment-section-with-emoji' : ''}} {{showEmojiList ? 'comment-section-with-emoji-list' : ''}} {{keyboardHeight > 0 ? 'comment-section-with-keyboard' : ''}}"
        style="{{keyboardHeight > 0 ? '--keyboard-height:' + keyboardHeight + 'px' : ''}}">

    <!-- 图片预览区域 - 最上面 -->
    <view class="image-preview-area" wx:if="{{selectedImages.length > 0}}">
      <view class="image-preview-item" wx:for="{{selectedImages}}" wx:key="index">
        <image src="{{item}}" mode="aspectFill" class="preview-image" bindtap="previewImage" data-index="{{index}}"></image>
        <view class="remove-image-btn" bindtap="removeImage" data-index="{{index}}">×</view>
      </view>
    </view>

    <!-- 常用表情栏 -->
    <view class="quick-emoji-bar">
      <view class="quick-emoji-item" wx:for="{{quickEmojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
        <text class="emoji-text">{{item}}</text>
      </view>
    </view>

    <!-- 评论输入框和工具栏 -->
    <view class="input-tools-container">
      <!-- 评论输入框 -->
      <textarea class="comment-textarea"
                placeholder="{{commentPlaceholder}}"
                value="{{commentContent}}"
                bindinput="onCommentInput"
                bindblur="handleInputBlur"
                bindfocus="handleInputFocus"
                bindkeyboardheightchange="handleKeyboardHeightChange"
                focus="{{commentInputFocused}}"
                fixed="true"
                show-confirm-bar="{{false}}"
                adjust-position="{{false}}"
                cursor-spacing="20"
                maxlength="100"
                auto-height="{{true}}"
                style="min-height: 80rpx; max-height: 200rpx;"
                hold-keyboard="{{true}}" />

      <!-- 工具栏 -->
      <view class="tools-row">
        <view class="left-tools">
          <view class="tool-item" bindtap="chooseImage">
            <image src="/images/tupian.png" class="tool-icon" />
            <view class="image-count" wx:if="{{selectedImages.length > 0}}">{{selectedImages.length}}</view>
          </view>
          <view class="tool-item" bindtap="openCamera">
            <image src="/images/xiangji.png" class="tool-icon" />
          </view>
          <view class="tool-item" bindtap="toggleEmojiList">
            <text class="emoji-icon">😊</text>
          </view>
        </view>
        <button class="submit-button {{isSubmittingComment ? 'disabled' : ''}}"
                bindtap="submitComment"
                disabled="{{isSubmittingComment}}">
          {{isSubmittingComment ? '发送中' : '发送'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 表情面板 -->
  <view class="emoji-panel" wx:if="{{showEmoji}}">
    <scroll-view scroll-y class="emoji-scroll">
      <view class="emoji-grid">
        <view class="emoji-item" wx:for="{{emojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
          <text class="emoji-text">{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- emoji表情面板 -->
  <view class="emoji-list-panel" wx:if="{{showEmojiList}}">
    <scroll-view scroll-y class="emoji-list-scroll">
      <view class="emoji-list-grid">
        <view class="emoji-list-item" wx:for="{{emojiList}}" wx:key="index" bindtap="selectEmoji" data-emoji="{{item}}">
          <text>{{item}}</text>
        </view>
      </view>
    </scroll-view>
  </view>

  <!-- 推荐菜品弹窗 -->
  <view wx:if="{{showDishModal}}" class="dish-modal-mask" bindtap="hideDishRecommendModal">
    <view class="dish-modal" catchtap="stopPropagation">
      <view class="dish-modal-header">
        <view class="dish-modal-title-section">
          <text class="dish-modal-title">推荐菜品</text>
          <text class="dish-modal-subtitle">点击标签即可推荐该菜品</text>
        </view>
        <view class="dish-modal-close" bindtap="hideDishRecommendModal">×</view>
      </view>

      <!-- 推荐菜品列表 -->
      <view class="dish-list" wx:if="{{dishList && dishList.length > 0}}">
        <view class="dish-capsule {{item.isRecommended ? 'recommended' : 'not-recommended'}}"
              wx:for="{{dishList}}" wx:key="dish_name"
              bindtap="toggleDishRecommend" data-dish="{{item.dish_name}}">
          <text class="dish-name">{{item.dish_name}}</text>
          <text class="dish-count">{{item.count}}</text>
        </view>
      </view>

      <!-- 无推荐菜品时显示 -->
      <view wx:if="{{!dishList || dishList.length === 0}}" class="no-dish">
        <text class="no-dish-text">暂无推荐菜品</text>
      </view>

      <!-- 添加新菜品 -->
      <view class="add-dish-section">
        <input class="dish-input"
               placeholder="输入菜品名称（最多10个字符）"
               value="{{newDishName}}"
               bindinput="onDishNameInput"
               maxlength="10" />
        <view class="add-dish-btn {{newDishName ? 'active' : ''}}"
              bindtap="addNewDish">立即添加</view>
      </view>
    </view>
  </view>

</view>
