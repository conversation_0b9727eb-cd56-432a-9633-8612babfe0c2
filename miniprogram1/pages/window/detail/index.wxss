/* 页面背景 */
.window-detail-bg {
  min-height: 100vh;
  background: #f8f9fa;
  padding-bottom: 120rpx;
}

/* 窗口信息头部 */
.window-header {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.window-info {
  display: flex;
  align-items: center;
  gap: 24rpx;
}

.window-avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 16rpx;
}

.window-details {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.window-name {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.window-tag {
  display: flex;
  align-items: center;
  gap: 4rpx;
}

.tag-icon {
  color: #5ec6fa;
  font-size: 28rpx;
  font-weight: 600;
}

.tag-text {
  color: #5ec6fa;
  font-size: 24rpx;
  font-weight: 600;
}

.tag-label {
  color: #5ec6fa;
  font-size: 28rpx;
}

.rating-display {
  text-align: right;
}

.rating-number {
  font-size: 48rpx;
  font-weight: 700;
  color: #5ec6fa;
}

.rating-unit {
  font-size: 28rpx;
  color: #5ec6fa;
  margin-left: 4rpx;
}

.rating-count {
  font-size: 24rpx;
  color: #999;
  margin-top: 8rpx;
}

/* 立即评分区域 */
.rating-section {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.rating-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24rpx;
}

.rating-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.rating-note {
  font-size: 24rpx;
  color: #999;
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  transition: all 0.3s ease;
}

.rating-note:active {
  background-color: rgba(0, 0, 0, 0.05);
  transform: scale(0.98);
}

.star-rating {
  display: flex;
  justify-content: space-between;
  margin-bottom: 16rpx;
  padding: 0 24rpx;
}

.star-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48rpx;
  height: 48rpx;
}

.star-icon {
  font-size: 36rpx;
  color: #e0e0e0;
  transition: all 0.3s ease;
}

.star-item.active .star-icon {
  color: #5ec6fa;
}

/* 负数评分样式 */
.star-item.active.negative .star-icon {
  color: #ff6b6b; /* 负数评分用红色 */
}

/* 正数评分样式 */
.star-item.active.positive .star-icon {
  color: #5ec6fa; /* 正数评分用蓝色 */
}

.rating-scale {
  display: flex;
  justify-content: space-between;
  padding: 0 24rpx;
  margin-bottom: 32rpx;
}

.scale-item {
  font-size: 24rpx;
  color: #999;
  width: 48rpx;
  text-align: center;
}

/* 推荐菜品区域 */
.dish-recommendation-section {
  background: #fff;
  padding: 32rpx;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
}

.dish-recommendation-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.dish-recommendation-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.recommend-dish-btn {
  background: #f8f9fa;
  border: 1rpx solid #e9ecef;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  transition: all 0.3s ease;
}

.recommend-dish-btn:active {
  transform: scale(0.98);
  background: #e9ecef;
  border-color: #dee2e6;
}

.recommend-btn-text {
  color: #6c757d;
  font-size: 24rpx;
  font-weight: 500;
}

/* 推荐菜品列表 */
.dish-recommendation-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  margin-top: 24rpx;
}

.dish-recommendation-item {
  display: flex;
  align-items: center;
  border-radius: 24rpx;
  padding: 12rpx 20rpx;
  gap: 8rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.dish-recommendation-item:active {
  transform: scale(0.98);
}

/* 未推荐的菜品 - 灰色 */
.dish-recommendation-item.not-recommended {
  background: #f5f5f5;
  border-color: #ddd;
}

.dish-recommendation-item.not-recommended:active {
  background: #e8e8e8;
}

.dish-recommendation-item.not-recommended .dish-name {
  font-size: 28rpx;
  color: #666;
  font-weight: 500;
}

.dish-recommendation-item.not-recommended .dish-count {
  font-size: 24rpx;
  color: #999;
  background: #e8e8e8;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
  font-weight: 600;
}

/* 已推荐的菜品 - 蓝色 */
.dish-recommendation-item.recommended {
  background: #f0f8ff;
  border-color: #5ec6fa;
}

.dish-recommendation-item.recommended:active {
  background: #e3f2fd;
}

.dish-recommendation-item.recommended .dish-name {
  font-size: 28rpx;
  color: #5ec6fa;
  font-weight: 600;
}

.dish-recommendation-item.recommended .dish-count {
  font-size: 24rpx;
  color: #4db8f0;
  background: #e3f2fd;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
  font-weight: 600;
}

.no-dish-recommendations {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 40rpx 0;
  margin-top: 24rpx;
}

.no-dish-recommendations-text {
  font-size: 28rpx;
  color: #999;
}

/* 全部评论 */
.comments-section {
  background: #fff;
  margin: 24rpx 32rpx;
  border-radius: 20rpx;
  box-shadow: 0 4rpx 16rpx rgba(0,0,0,0.05);
  overflow: hidden;
}

.comments-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 32rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.comments-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.sort-tabs {
  display: flex;
  gap: 32rpx;
}

.sort-tab {
  font-size: 28rpx;
  color: #999;
  position: relative;
}

.sort-tab.active {
  color: #5ec6fa;
  font-weight: 600;
}

.sort-tab.active::after {
  content: '';
  position: absolute;
  bottom: -8rpx;
  left: 50%;
  transform: translateX(-50%);
  width: 24rpx;
  height: 4rpx;
  background: #5ec6fa;
  border-radius: 2rpx;
}

/* 评论列表 */
.comments-list {
  padding: 0 32rpx 32rpx;
}

.comment-item {
  display: flex;
  gap: 24rpx;
  padding: 32rpx 0;
  border-bottom: 2rpx solid #f8f9fa;
}

.comment-item:last-child {
  border-bottom: none;
}

.comment-avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  flex-shrink: 0;
}

.comment-content {
  flex: 1;
}

.comment-header {
  margin-bottom: 12rpx;
}

.comment-username {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
}

.comment-text {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.comment-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.comment-time {
  font-size: 24rpx;
  color: #999;
}

.comment-reply {
  font-size: 24rpx;
  color: #5ec6fa;
}

.comment-actions {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 10rpx 15rpx; /* 增加点击区域 */
  margin: -10rpx -15rpx; /* 负边距保持视觉位置不变 */
  border-radius: 20rpx; /* 圆角让点击区域更自然 */
}

.comment-actions .action-icon {
  width: 28rpx;
  height: 28rpx;
}

.action-count {
  font-size: 24rpx;
  color: #999;
}

/* 回复列表样式 */
.replies-list {
  margin-top: 24rpx;
  padding-left: 20rpx; /* 减少左侧padding */
  border-left: 2rpx solid #f0f0f0;
}

.reply-item {
  display: flex;
  gap: 16rpx;
  padding: 16rpx 0;
  border-bottom: 1rpx solid #f8f9fa;
}

.reply-item:last-child {
  border-bottom: none;
}

.reply-avatar {
  width: 60rpx;
  height: 60rpx;
  border-radius: 8rpx;
  flex-shrink: 0;
}

.reply-content {
  flex: 1;
}

.reply-header {
  margin-bottom: 8rpx;
}

.reply-username {
  font-size: 24rpx;
  font-weight: 600;
  color: #333;
}

.reply-text {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  margin-bottom: 8rpx;
}

.reply-prefix {
  color: #999; /* 回复xxx: 使用灰色 */
}

.reply-main-content {
  color: #333;
}

.reply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.reply-time {
  font-size: 22rpx;
  color: #999;
}

.reply-actions {
  display: flex;
  align-items: center;
  gap: 6rpx;
  padding: 10rpx 15rpx; /* 增加左右padding */
  margin: -10rpx -15rpx; /* 对应的负边距 */
  border-radius: 16rpx; /* 圆角让点击区域更自然 */
}

.reply-action-icon {
  width: 28rpx;
  height: 28rpx;
  margin-right: 6rpx;
}

.reply-action-count {
  font-size: 22rpx;
  color: #666;
}

/* 无评论样式 */
.no-comment {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
  margin: 20rpx 40rpx;
  margin-top: 100rpx;
}

.no-comment-image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 20rpx;
}

.no-comment-text {
  font-size: 28rpx;
  color: #999;
  text-align: center;
}

/* 底部遮罩 */
.bottom-mask {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 150rpx;
  background: linear-gradient(to top, rgba(248, 249, 250, 1) 0%, rgba(248, 249, 250, 0.8) 50%, rgba(248, 249, 250, 0) 100%);
  z-index: 99;
  pointer-events: none;
}

/* 底部输入框 */
.bottom-fixed-bar {
  position: fixed;
  bottom: 50rpx;
  left: 24rpx;
  right: 24rpx;
  width: auto;
  background-color: #fff;
  padding: 16rpx 24rpx;
  box-shadow: 0 0 10rpx rgba(0, 0, 0, 0.1);
  display: flex;
  justify-content: space-between;
  align-items: center;
  z-index: 100;
  box-sizing: border-box;
  border-radius: 40rpx;
}

.input-section {
  flex: 1;
  display: flex;
  align-items: center;
  background-color: #f5f5f5;
  border-radius: 36rpx;
  padding: 15rpx 30rpx;
  margin-right: 30rpx;
}

.comment-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 15rpx;
}

.input-placeholder {
  color: #999;
  font-size: 28rpx;
}

.action-section {
  display: flex;
  align-items: center;
  gap: 32rpx;
}

.action-item {
  display: flex;
  align-items: center;
  padding: 15rpx;
  margin: -15rpx;
  -webkit-tap-highlight-color: transparent !important;
  background-color: transparent !important;
}

.action-item:active {
  background-color: transparent !important;
}

.action-icon {
  width: 35rpx;
  height: 35rpx;
}

.action-text {
  font-size: 28rpx;
  color: #666;
  margin-left: 10rpx;
}

/* 评论遮罩和输入框样式 - 参考messagedetail页面 */
.comment-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.3);
  z-index: 998;
}

.fixed-comment-section {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100%;
  background-color: #fff;
  padding: 12rpx 24rpx;
  z-index: 999;
  display: flex;
  flex-direction: column;
  gap: 12rpx;
  box-sizing: border-box;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  border-top-left-radius: 24rpx;
  border-top-right-radius: 24rpx;
  transform: translateY(0);
  transition: transform 0.3s ease-out;
}

.comment-section-with-keyboard {
  transform: translateY(calc(-1 * var(--keyboard-height)));
}

.comment-section-with-emoji {
  transform: translateY(-400rpx);
}

.comment-section-with-emoji-list {
  transform: translateY(-400rpx);
}



/* 常用表情栏 */
.quick-emoji-bar {
  display: flex;
  justify-content: space-around;
  align-items: center;
  padding: 16rpx 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.quick-emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.quick-emoji-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.emoji-text {
  font-size: 36rpx;
  line-height: 1;
}

/* 输入框和工具栏容器 */
.input-tools-container {
  display: flex;
  flex-direction: column;
  background: #fff;
}

.comment-textarea {
  width: 100%;
  background-color: #f5f5f5;
  border-radius: 18rpx;
  padding: 20rpx 24rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  line-height: 1.4;
  margin: 12rpx 24rpx 8rpx 24rpx;
  min-height: 80rpx;
  max-height: 200rpx;
  overflow-y: auto;
  transition: all 0.3s ease;
}

/* 表情面板 */
.emoji-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1001;
  border-top: 1rpx solid #e0e0e0;
}

.emoji-scroll {
  height: 100%;
  padding: 20rpx;
}

.emoji-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.emoji-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 80rpx;
  height: 80rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  transition: all 0.3s ease;
}

.emoji-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

.emoji-item .emoji-text {
  font-size: 40rpx;
}

/* emoji表情面板 */
.emoji-list-panel {
  position: fixed;
  left: 0;
  right: 0;
  bottom: 0;
  height: 400rpx;
  background: #fff;
  z-index: 1001;
  border-top: 1rpx solid #e0e0e0;
}

.emoji-list-scroll {
  height: 100%;
  padding: 20rpx;
}

.emoji-list-grid {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.emoji-list-item {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 72rpx;
  height: 72rpx;
  border-radius: 12rpx;
  background: #f8f9fa;
  font-size: 36rpx;
  transition: all 0.3s ease;
}

.emoji-list-item:active {
  background: #e9ecef;
  transform: scale(0.95);
}

/* 图片预览区域样式 - 在最上面 */
.image-preview-area {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
  padding: 16rpx 24rpx 0 24rpx;
  background: #fff;
  border-bottom: 1rpx solid #f0f0f0;
}

.image-preview-item {
  position: relative;
  width: 140rpx;
  height: 140rpx;
  border-radius: 16rpx;
  overflow: hidden;
  background: #f8f9fa;
}

.preview-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.remove-image-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 36rpx;
  height: 36rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
  z-index: 10;
  box-shadow: 0 2rpx 8rpx rgba(255, 71, 87, 0.3);
}

/* messagedetail样式的工具栏 */
.tools-row {
  position: relative;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8rpx 24rpx 16rpx 24rpx;
  height: 72rpx;
  background-color: #fff;
  border-radius: 0 0 24rpx 24rpx;
}

.left-tools {
  display: flex;
  gap: 20rpx;
  align-items: center;
  flex: 1;
}

.tool-item {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 50rpx;
  height: 50rpx;
  padding: 10rpx;
  border-radius: 50%;
}

.tool-item:active {
  background-color: #f5f5f5;
}

.tool-icon {
  width: 50rpx;
  height: 50rpx;
}

.emoji-icon {
  font-size: 40rpx;
  line-height: 1;
}

.image-count {
  position: absolute;
  top: -5rpx;
  right: -5rpx;
  background: #ff4757;
  color: white;
  border-radius: 50%;
  width: 32rpx;
  height: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 20rpx;
  font-weight: bold;
}

.submit-button {
  background: #5ec6fa;
  color: white;
  border: none;
  border-radius: 36rpx;
  padding: 16rpx 32rpx;
  font-size: 28rpx;
  font-weight: 500;
  min-width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: auto;
  flex-shrink: 0;
}

.submit-button.disabled {
  background: #cccccc;
  color: #999999;
}

/* 评论图片样式 */
.comment-images {
  display: flex;
  gap: 8rpx;
  margin-top: 16rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.comment-image {
  width: calc(33.33% - 6rpx);
  height: 160rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

/* 回复图片样式 */
.reply-images {
  display: flex;
  gap: 8rpx;
  margin-top: 12rpx;
  margin-bottom: 10rpx;
  width: 100%;
}

.reply-image {
  width: calc(33.33% - 6rpx);
  height: 120rpx;
  border-radius: 8rpx;
  background: #f5f5f5;
  flex-shrink: 0;
}

/* 推荐菜品弹窗样式 */
.dish-modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 1000;
  display: flex;
  align-items: flex-end;
  justify-content: center;
}

.dish-modal {
  background: #fff;
  border-radius: 24rpx 24rpx 0 0;
  width: 100%;
  max-height: 70vh;
  padding: 32rpx;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    transform: translateY(100%);
  }
  to {
    transform: translateY(0);
  }
}

.dish-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 32rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.dish-modal-title-section {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.dish-modal-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
}

.dish-modal-subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: 400;
}

.dish-modal-close {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  color: #999;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.dish-modal-close:active {
  background: #f5f5f5;
  transform: scale(0.95);
}

.dish-list {
  max-height: 300rpx;
  overflow-y: auto;
  margin-bottom: 32rpx;
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.dish-capsule {
  display: flex;
  align-items: center;
  padding: 12rpx 20rpx;
  border-radius: 24rpx;
  transition: all 0.3s ease;
  border: 2rpx solid transparent;
  gap: 8rpx;
}

/* 未推荐的菜品 - 灰色胶囊 */
.dish-capsule.not-recommended {
  background: #f5f5f5;
  border-color: #ddd;
}

.dish-capsule.not-recommended:active {
  transform: scale(0.98);
  background: #e8e8e8;
}

.dish-capsule.not-recommended .dish-name {
  color: #666;
  font-size: 28rpx;
}

.dish-capsule.not-recommended .dish-count {
  color: #999;
  font-size: 24rpx;
  background: #e8e8e8;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
}

/* 已推荐的菜品 - 蓝色胶囊 */
.dish-capsule.recommended {
  background: #f0f8ff;
  border-color: #5ec6fa;
}

.dish-capsule.recommended:active {
  transform: scale(0.98);
  background: #e3f2fd;
}

.dish-capsule.recommended .dish-name {
  color: #5ec6fa;
  font-size: 28rpx;
  font-weight: 600;
}

.dish-capsule.recommended .dish-count {
  color: #4db8f0;
  font-size: 24rpx;
  background: #e3f2fd;
  border-radius: 12rpx;
  padding: 4rpx 8rpx;
  min-width: 32rpx;
  text-align: center;
  line-height: 1;
  font-weight: 600;
}

.no-dish {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 60rpx 0;
  margin-bottom: 32rpx;
}

.no-dish-text {
  font-size: 28rpx;
  color: #999;
}

.add-dish-section {
  display: flex;
  gap: 16rpx;
  align-items: center;
  padding-top: 16rpx;
  border-top: 2rpx solid #f0f0f0;
}

.dish-input {
  flex: 1;
  background: #f5f5f5;
  border-radius: 12rpx;
  padding: 16rpx 20rpx;
  font-size: 28rpx;
  color: #333;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.dish-input:focus {
  background: #fff;
  border-color: #5ec6fa;
}

.add-dish-btn {
  background: #f5f5f5;
  color: #999;
  border-radius: 12rpx;
  padding: 16rpx 24rpx;
  font-size: 28rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
}

.add-dish-btn.active {
  background: #5ec6fa;
  color: #fff;
}

.add-dish-btn:active {
  transform: scale(0.98);
}

