/**
 * 图片URL处理工具
 */

/**
 * 处理图片URL，为上传的图片添加域名前缀
 * @param {string} imageUrl - 原始图片URL
 * @param {string} defaultUrl - 默认图片URL
 * @returns {string} 处理后的完整URL
 */
function processImageUrl(imageUrl, defaultUrl = '/images/shitang.png') {
  if (!imageUrl) {
    return defaultUrl
  }
  
  // 如果是上传的图片（以/uploads/开头），添加域名前缀
  if (imageUrl.startsWith('/uploads/')) {
    return getApp().globalData.wangz + imageUrl
  }
  
  // 如果已经是完整URL（包含http），直接返回
  if (imageUrl.startsWith('http')) {
    return imageUrl
  }
  
  // 其他情况（如本地图片路径），直接返回
  return imageUrl
}

/**
 * 批量处理图片URL
 * @param {Array} items - 包含图片URL的对象数组
 * @param {string} imageField - 图片字段名，默认为'img'
 * @param {string} defaultUrl - 默认图片URL
 * @returns {Array} 处理后的数组
 */
function processImageUrls(items, imageField = 'img', defaultUrl = '/images/shitang.png') {
  if (!Array.isArray(items)) {
    return items
  }
  
  return items.map(item => {
    if (typeof item === 'object' && item[imageField]) {
      return {
        ...item,
        [imageField]: processImageUrl(item[imageField], defaultUrl)
      }
    }
    return item
  })
}

/**
 * 处理用户头像URL
 * @param {string} avatarUrl - 原始头像URL
 * @returns {string} 处理后的完整URL
 */
function processAvatarUrl(avatarUrl) {
  return processImageUrl(avatarUrl, '/images/default_avatar.png')
}

module.exports = {
  processImageUrl,
  processImageUrls,
  processAvatarUrl
}
