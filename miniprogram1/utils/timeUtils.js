// utils/timeUtils.js
const timeUtils = {
  // 格式化时间显示
  formatTime(timeStr) {
    if (!timeStr) return ''
    
    const now = new Date()
    const time = new Date(timeStr)
    const diff = now.getTime() - time.getTime()
    
    // 计算时间差（毫秒）
    const minute = 60 * 1000
    const hour = 60 * minute
    const day = 24 * hour
    
    if (diff < hour) {
      // 1小时内显示"x分钟前"
      const minutes = Math.floor(diff / minute)
      return minutes <= 0 ? '刚刚' : `${minutes}分钟前`
    } else if (diff < day) {
      // 1天内显示"x小时前"
      const hours = Math.floor(diff / hour)
      return `${hours}小时前`
    } else if (diff < 3 * day) {
      // 3天内显示"x天前"
      const days = Math.floor(diff / day)
      return `${days}天前`
    } else {
      // 超过3天显示具体日期 YY-MM-DD
      const year = time.getFullYear().toString().slice(-2)
      const month = (time.getMonth() + 1).toString().padStart(2, '0')
      const date = time.getDate().toString().padStart(2, '0')
      return `${year}-${month}-${date}`
    }
  },

  // 格式化完整时间
  formatFullTime(timeStr) {
    if (!timeStr) return ''
    
    const time = new Date(timeStr)
    const year = time.getFullYear()
    const month = (time.getMonth() + 1).toString().padStart(2, '0')
    const date = time.getDate().toString().padStart(2, '0')
    const hours = time.getHours().toString().padStart(2, '0')
    const minutes = time.getMinutes().toString().padStart(2, '0')
    
    return `${year}-${month}-${date} ${hours}:${minutes}`
  },

  // 格式化日期
  formatDate(timeStr) {
    if (!timeStr) return ''
    
    const time = new Date(timeStr)
    const year = time.getFullYear().toString().slice(-2)
    const month = (time.getMonth() + 1).toString().padStart(2, '0')
    const date = time.getDate().toString().padStart(2, '0')
    
    return `${year}-${month}-${date}`
  },

  // 获取当前时间戳
  now() {
    return new Date().getTime()
  },

  // 判断是否是今天
  isToday(timeStr) {
    if (!timeStr) return false
    
    const today = new Date()
    const time = new Date(timeStr)
    
    return today.getFullYear() === time.getFullYear() &&
           today.getMonth() === time.getMonth() &&
           today.getDate() === time.getDate()
  },

  // 判断是否是昨天
  isYesterday(timeStr) {
    if (!timeStr) return false
    
    const yesterday = new Date()
    yesterday.setDate(yesterday.getDate() - 1)
    const time = new Date(timeStr)
    
    return yesterday.getFullYear() === time.getFullYear() &&
           yesterday.getMonth() === time.getMonth() &&
           yesterday.getDate() === time.getDate()
  }
}

module.exports = timeUtils
