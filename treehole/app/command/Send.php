<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db; // 引入数据库操作类
use think\facade\Log; // 引入日志类

class SendMessage extends Command
{
    protected function configure()
    {
        // 设置命令名称和描述
        $this->setName('send:message')
             ->setDescription('每15分钟发送一次消息');
    }

    protected function execute(Input $input, Output $output)
    {
        // 数据库查询，获取需要发送的内容
        try {
            // 从 messages 表中获取最新的一条消息
            $message = Db::name('messages')
                         ->order('id', 'desc')
                         ->value('content');

            if (!$message) {
                $output->writeln('未找到需要发送的消息内容。');
                Log::warning('未找到需要发送的消息内容。');
                return;
            }
        } catch (\Exception $e) {
            $output->writeln('数据库查询出错：' . $e->getMessage());
            Log::error('数据库查询出错：' . $e->getMessage());
            return;
        }

        // 将查询到的内容替换到请求数据中
        $postData = [
            "socketType" => 2,
            "list" => [
                [
                    "type" => 203,
                    "titleList" => [
                        "仑哥(这里改成你的微信昵称或群名)"
                    ],
                    "receivedContent" => $message
                ]
            ]
        ];

        // 初始化 cURL
        $curl = curl_init();

        curl_setopt_array($curl, array(
            CURLOPT_URL => 'https://api.worktool.ymdyes.cn/wework/sendRawMessage?robotId=%7B%7Brobot_id%7D%7D',
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => '',
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 0,
            CURLOPT_FOLLOWLOCATION => true,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => 'POST',
            CURLOPT_POSTFIELDS => json_encode($postData, JSON_UNESCAPED_UNICODE),
            CURLOPT_HTTPHEADER => array(
                'Content-Type: application/json'
            ),
        ));

        $response = curl_exec($curl);
        $error = curl_error($curl);
        curl_close($curl);

        if ($response === false) {
            $output->writeln('接口调用失败：' . $error);
            Log::error('接口调用失败：' . $error);
        } else {
            $output->writeln('接口调用成功：' . $response);
            Log::info('接口调用成功：' . $response);
        }
    }
}