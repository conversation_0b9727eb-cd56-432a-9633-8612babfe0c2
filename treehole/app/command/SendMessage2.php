<?php
namespace app\command;

use think\console\Command;
use think\console\Input;
use think\console\Output;
use think\facade\Db; // 引入数据库操作类
use think\facade\Log; // 引入日志类

class SendMessage2 extends Command
{
    protected function configure()
    {
        // 设置命令名称和描述
        $this->setName('send:message2')
            ->setDescription('每15分钟发送一次消息');
    }

    protected function execute(Input $input, Output $output)
    {
        try {
            // 从 message 表中获取按 id 倒序的前 45 条消息
            
            $messages = Db::name('message')
                        ->where('choose', '<', 100) // 筛选 choose 字段小于 100
                        ->order('id', 'desc')
                        ->limit(100)
                        ->select()
                        ->toArray();
            $collectedMessages = [];
            $idsToUpdate = []; // 保存需要更新的消息 ID

            if ($messages) {
                foreach ($messages as $message) {
                    if ($message['ispull'] == 0) {
                        // 如果 ispull 为 0，停止查找
                        break;
                    }

                    // 如果有换行，只取换行前的内容；否则限制前 28 个字符
                    $content = $message['content'];
                    $newlinePos = strpos($content, "\n");
                    if ($newlinePos !== false) {
                        // 如果找到换行符，只取换行前的内容
                        $limitedContent = substr($content, 0, $newlinePos);
                    } else {
                        // 如果没有换行符，限制前 28 个字符
                        $limitedContent = mb_substr($content, 0, 28, 'UTF-8');
                    }

                    // 收集消息内容
                    $formattedMessage = $limitedContent . "\n" . $message['scheme'] . "\n";
                    $collectedMessages[] = $formattedMessage;

                    // 记录需要更新的消息 ID
                    $idsToUpdate[] = $message['id'];
                }
            }
            // 打印到日志文件


        } catch (\Exception $e) {
            $output->writeln('数据库查询出错：' . $e->getMessage());
            Log::error('数据库查询出错：' . $e->getMessage());
            return;
        }

        // 将消息分成每组最多 10 条，并倒序发送
        $messageChunks = array_chunk($collectedMessages, 10);
        $messageChunks = array_reverse($messageChunks); // 将整个批次的顺序倒过来

        foreach ($messageChunks as $chunk) {
            // 将消息倒序排列
            $chunk = array_reverse($chunk);

            // 将消息合并成一个字符串，使用 \n 分隔
            $receivedContent = implode("\n", $chunk);

            // 构建请求数据
            $postData = [
                "socketType" => 2,
                "list" => [
                    [
                        "type" => 203,
                        "titleList" => [
                            "灵行北航校内论坛5"

                        ],
                        "receivedContent" => $receivedContent
                    ]
                ]
            ];

            // 初始化 cURL
            $curl = curl_init();

            curl_setopt_array($curl, array(
                CURLOPT_URL => 'https://api.worktool.ymdyes.cn/wework/sendRawMessage?robotId=wt6f671p34nvtpr22xfq25xc085nfg4u',
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => '',
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 0,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => 'POST',
                CURLOPT_POSTFIELDS => json_encode($postData, JSON_UNESCAPED_UNICODE),
                CURLOPT_HTTPHEADER => array(
                    'Content-Type: application/json'
                ),
            ));

            $response = curl_exec($curl);
            $error = curl_error($curl);
            curl_close($curl);

            if ($response === false) {
                $output->writeln('接口调用失败：' . $error);
                Log::error('接口调用失败：' . $error);
            } else {
                $output->writeln('接口调用成功：' . $response);
                Log::info('接口调用成功：' . $response);

                // 接口调用成功后更新 ispull 字段为 0
                if (!empty($idsToUpdate)) {
                    foreach ($idsToUpdate as $id) {
                        try {
                            Db::name('message')
                                ->where('id', $id)
                                ->update(['ispull' => 0]);
                            Log::info("成功将消息 ID {$id} 的 ispull 设置为 0。");
                        } catch (\Exception $e) {
                            $output->writeln("更新消息 ID {$id} 的 ispull 字段失败：" . $e->getMessage());
                            Log::error("更新消息 ID {$id} 的 ispull 字段失败：" . $e->getMessage());
                        }
                    }
                }
            }
            // 如果有多组消息，批次间隔 1 分钟

        }
    }
}