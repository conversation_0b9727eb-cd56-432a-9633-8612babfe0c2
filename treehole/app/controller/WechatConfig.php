<?php
declare (strict_types = 1);

namespace app\controller;

use think\response\Json;

class WechatConfig
{
    /**
     * 获取公众号链接配置
     */
    public function getConfig(): Json
    {
        try {
            // 直接获取配置，无需token认证
            $config = $this->getWechatLinkConfig();

            return json([
                'code' => 200,
                'msg' => '获取成功',
                'data' => $config
            ]);

        } catch (\Exception $e) {
            return json([
                'code' => 500,
                'msg' => '服务器错误：' . $e->getMessage()
            ]);
        }
    }

    /**
     * 获取公众号链接配置
     * 如需修改配置，直接修改此方法中的配置数组即可
     */
    private function getWechatLinkConfig(): array
    {
        // 公众号链接配置
        // 如需修改，直接修改下面的配置即可
        return [
            // 是否显示公众号链接（true显示，false隐藏）
            'show_wechat_link' => true,

            // 公众号文章链接
            'wechat_url' => 'https://mp.weixin.qq.com/s/5wrW4T-N7IQSNDmZCLngYQ',

            // 公众号链接图片
            'wechat_image' => 'https://www.bjgaoxiaoshequ.store/uploads/huodong.png',

            // 公众号链接标题
            'wechat_title' => '点击图片进入公众号内参与活动，100%送金属贴，另有明信片、现金红包等'
        ];
    }
}
