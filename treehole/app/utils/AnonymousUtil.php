<?php
namespace app\utils;

/**
 * 匿名用户名生成工具类
 */
class AnonymousUtil
{
    /**
     * 生成匿名用户名
     * 格式：匿·形容词的名词
     * 
     * @param int $userId 用户ID
     * @param int $messageId 帖子ID
     * @return string 匿名用户名
     */
    public static function generateAnonymousName($userId, $messageId)
    {
                // 100个形容词
        $adjectives = [
            '温柔', '安静', '勇敢', '善良', '聪明', '可爱', '神秘', '快乐',
            '优雅', '淡然', '从容', '坚强', '柔和', '清新', '纯真', '活泼',
            '沉稳', '热情', '冷静', '机智', '幽默', '真诚', '朴实', '细腻',
            '豁达', '洒脱', '文静', '开朗', '内敛', '外向', '理性', '感性',
            '乐观', '悲观', '积极', '消极', '主动', '被动', '直率', '含蓄',
            '大方', '小心', '谨慎', '随性', '严谨', '松散', '专注', '散漫',
            '独立', '依赖', '温暖', '清冷', '明亮', '深邃', '轻盈', '厚重',
            '灵动', '稳重', '飘逸', '端庄', '俏皮', '庄重', '清雅', '华丽',
            '简约', '繁复', '古典', '现代', '传统', '前卫', '保守', '激进',
            '温和', '激烈', '平静', '狂野', '温顺', '叛逆', '乖巧', '顽皮',
            '纯洁', '复杂', '单纯', '深沉', '浅显', '深刻', '表面', '内在',
            '外露', '内敛', '张扬', '低调', '高调', '朴素', '华贵', '平凡',
            '特别', '普通', '独特', '常见', '罕见', '珍贵', '平价', '昂贵'
        ];

        // 100个名词
        $nouns = [
            '清风', '暖阳', '细雨', '晨光', '夜空', '星辰', '月影', '花香',
            '鸟语', '溪流', '山峦', '云朵', '彩虹', '微风', '朝霞', '夕阳',
            '雪花', '春雨', '秋叶', '冬梅', '夏荷', '青山', '绿水', '白云',
            '蓝天', '紫霞', '金辉', '银月', '翠竹', '红梅', '黄花', '粉樱',
            '碧海', '青松', '白鹤', '黑燕', '灰鸽', '棕熊', '绿萝', '紫藤',
            '橙光', '蓝影', '红叶', '白雪', '黑夜', '灰云', '棕土', '绿草',
            '紫花', '橙果', '银河', '金沙', '玉露', '珍珠', '琥珀', '水晶',
            '翡翠', '玛瑙', '钻石', '宝石', '明珠', '璀璨', '光芒', '辉煌',
            '灿烂', '闪耀', '光辉', '亮光', '火花', '星火', '烛光', '灯火',
            '月光', '阳光', '霞光', '晨曦', '暮色', '黄昏', '黎明', '午夜',
            '春风', '夏雨', '秋霜', '冬雪', '梅花', '兰草', '竹叶', '菊香',
            '桃花', '杏花', '梨花', '荷花', '牡丹', '玫瑰', '茉莉', '桂花',
            '丁香', '紫罗兰', '向日葵', '百合'
        ];
        
        // 使用用户ID和帖子ID生成哈希，确保同一用户在同一帖子下名称一致
        $hash = md5($userId . '_' . $messageId);
        
        // 从哈希中提取索引
        $adjIndex = hexdec(substr($hash, 0, 4)) % count($adjectives);
        $nounIndex = hexdec(substr($hash, 4, 4)) % count($nouns);
        
        return '匿·' . $adjectives[$adjIndex] . '的' . $nouns[$nounIndex];
    }
    
    /**
     * 生成匿名头像URL
     *
     * @param int $userId 用户ID
     * @param int $messageId 帖子ID
     * @return string 头像URL
     */
    public static function generateAnonymousAvatar($userId, $messageId)
    {
        // 使用用户ID和帖子ID生成哈希，确保同一用户在同一帖子下头像一致
        $hash = md5($userId . '_' . $messageId);

        // 从1到32的随机头像编号
        $avatarNumber = hexdec(substr($hash, 0, 4)) % 32 + 1;

        // 使用image1.png到image32.png的随机头像
        return "https://www.bjgaoxiaoshequ.store/images/image{$avatarNumber}.png";
    }

    /**
     * 生成匿名头像颜色（备用方案）
     *
     * @param string $anonymousName 匿名用户名
     * @return string 颜色值
     */
    public static function generateAnonymousAvatarColor($anonymousName)
    {
        $colors = [
            '#FF6B6B', '#4ECDC4', '#45B7D1', '#96CEB4',
            '#FFEAA7', '#DDA0DD', '#98D8C8', '#F7DC6F'
        ];

        $hash = md5($anonymousName);
        $index = hexdec(substr($hash, 0, 2)) % count($colors);

        return $colors[$index];
    }
    
    /**
     * 处理匿名帖子数据
     * 清理敏感信息并添加匿名显示信息
     * 
     * @param array $postData 帖子数据
     * @param bool $isOriginalPoster 是否是原帖作者
     * @return array 处理后的数据
     */
    public static function processAnonymousPostData($postData, $isOriginalPoster = false)
    {
        if (!isset($postData['is_anonymous']) || !$postData['is_anonymous']) {
            return $postData;
        }
        
        $originalUserId = $postData['user_id'];
        $messageId = $postData['id'];

        // 生成匿名名称和头像
        $anonymousName = self::generateAnonymousName($originalUserId, $messageId);
        $anonymousAvatar = self::generateAnonymousAvatar($originalUserId, $messageId);

        // 设置匿名显示信息
        $postData['username'] = $anonymousName;
        $postData['anonymous_name'] = $anonymousName;
        $postData['face_url'] = $anonymousAvatar;
        $postData['is_anonymous_display'] = true;
        $postData['is_original_poster'] = $isOriginalPoster;
        $postData['titlename'] = $isOriginalPoster ? '帖主' : '';
        $postData['anonymous_avatar_color'] = self::generateAnonymousAvatarColor($anonymousName);
        $postData['original_user_id'] = $originalUserId; // 保留原始用户ID用于前端功能
        
        return $postData;
    }
    
    /**
     * 处理匿名评论数据
     *
     * @param array $commentData 评论数据
     * @param int $originalPosterId 原帖作者ID
     * @param int $messageId 帖子ID
     * @param bool $isFromAnonymousPost 是否来自匿名帖子
     * @return array 处理后的数据
     */
    public static function processAnonymousCommentData($commentData, $originalPosterId, $messageId, $isFromAnonymousPost = false)
    {
        if (!$isFromAnonymousPost) {
            return $commentData;
        }

        // 检查是否有必要的字段
        if (!isset($commentData['user_id'])) {
            return $commentData;
        }

        // 生成匿名名称
        $anonymousName = self::generateAnonymousName($commentData['user_id'], $messageId);

        // 检查是否是原帖作者
        $isOriginalPoster = ($commentData['user_id'] == $originalPosterId);

        // 保存原始user_id用于后端逻辑判断，但不返回给前端
        $originalUserId = $commentData['user_id'];

        // 清理敏感信息（但保留user_id用于内部逻辑）
        if (isset($commentData['openid'])) unset($commentData['openid']);
        if (isset($commentData['phone'])) unset($commentData['phone']);
        if (isset($commentData['xuehao'])) unset($commentData['xuehao']);

        // 设置匿名显示信息
        $commentData['username'] = $anonymousName;
        $commentData['anonymous_name'] = $anonymousName;
        $commentData['face_url'] = self::generateAnonymousAvatar($originalUserId, $messageId);
        $commentData['is_original_poster'] = $isOriginalPoster;
        $commentData['titlename'] = $isOriginalPoster ? '帖主' : '';
        $commentData['titlecolor'] = $isOriginalPoster ? '0' : ($commentData['titlecolor'] ?? '0'); // 帖主使用灰色，其他保持原色
        $commentData['anonymous_avatar_color'] = self::generateAnonymousAvatarColor($anonymousName);
        $commentData['original_user_id'] = $originalUserId; // 保留原始用户ID用于前端回复功能

        // 调试日志
        error_log("AnonymousUtil处理评论数据 - 用户ID: {$originalUserId}, 原帖作者ID: {$originalPosterId}, 是否帖主: " . ($isOriginalPoster ? 'true' : 'false') . ", 匿名名称: {$anonymousName}");

        return $commentData;
    }
    
    /**
     * 批量处理匿名评论数据
     * 
     * @param array $commentList 评论列表
     * @param int $originalPosterId 原帖作者ID
     * @param int $messageId 帖子ID
     * @param bool $isFromAnonymousPost 是否来自匿名帖子
     * @return array 处理后的评论列表
     */
    public static function processAnonymousCommentList($commentList, $originalPosterId, $messageId, $isFromAnonymousPost = false)
    {
        if (!$isFromAnonymousPost || empty($commentList)) {
            return $commentList;
        }
        
        foreach ($commentList as &$comment) {
            $comment = self::processAnonymousCommentData($comment, $originalPosterId, $messageId, $isFromAnonymousPost);
            
            // 处理回复数据
            if (isset($comment['replies']) && is_array($comment['replies'])) {
                foreach ($comment['replies'] as &$reply) {
                    $reply = self::processAnonymousCommentData($reply, $originalPosterId, $messageId, $isFromAnonymousPost);

                    // 为回复也添加匿名名称处理
                    if ($isFromAnonymousPost && isset($reply['user_id'])) {
                        $reply['anonymous_name'] = self::generateAnonymousName($reply['user_id'], $messageId);
                        $reply['original_user_id'] = $reply['user_id'];

                        // 如果回复中有 reply_to_username，也需要转换为匿名名称
                        if (isset($reply['reply_to_user_id'])) {
                            $reply['reply_to_anonymous_name'] = self::generateAnonymousName($reply['reply_to_user_id'], $messageId);
                        }
                    }
                }
            }
        }
        
        return $commentList;
    }
    
    /**
     * 检查是否是匿名帖子
     *
     * @param array $postData 帖子数据
     * @return bool
     */
    public static function isAnonymousPost($postData)
    {
        return isset($postData['is_anonymous']) && $postData['is_anonymous'] == 1;
    }

}
