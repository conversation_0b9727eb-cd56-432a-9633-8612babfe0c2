-- 为匿名功能添加数据库字段
-- 执行前请备份数据库

-- 1. 为message表添加匿名字段
ALTER TABLE `message` 
ADD COLUMN `is_anonymous` tinyint(1) DEFAULT 0 COMMENT '是否匿名发布：0-实名，1-匿名' AFTER `views`;

-- 2. 为comment表添加匿名相关字段
ALTER TABLE `comment`
ADD COLUMN `anonymous_name` varchar(50) DEFAULT NULL COMMENT '匿名显示名称' AFTER `titlecolor`,
ADD COLUMN `anonymous_avatar` varchar(255) DEFAULT NULL COMMENT '匿名头像URL' AFTER `anonymous_name`,
ADD COLUMN `is_from_anonymous_post` tinyint(1) DEFAULT 0 COMMENT '是否来自匿名帖子：0-否，1-是' AFTER `anonymous_avatar`;

-- 3. 为post表添加匿名相关字段
ALTER TABLE `post`
ADD COLUMN `anonymous_name` varchar(50) DEFAULT NULL COMMENT '匿名显示名称' AFTER `titlecolor`,
ADD COLUMN `anonymous_avatar` varchar(255) DEFAULT NULL COMMENT '匿名头像URL' AFTER `anonymous_name`,
ADD COLUMN `is_from_anonymous_post` tinyint(1) DEFAULT 0 COMMENT '是否来自匿名帖子：0-否，1-是' AFTER `anonymous_avatar`;

-- 4. 为notification表添加匿名相关字段（用于通知显示）
ALTER TABLE `notification`
ADD COLUMN `is_anonymous` tinyint(1) DEFAULT 0 COMMENT '是否匿名通知：0-否，1-是' AFTER `is_read`,
ADD COLUMN `anonymous_name` varchar(50) DEFAULT NULL COMMENT '匿名用户名（用于通知显示）' AFTER `is_anonymous`;

-- 5. 创建匿名名称映射表（可选，用于缓存匿名名称和头像）
CREATE TABLE IF NOT EXISTS `anonymous_mapping` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '映射ID',
  `message_id` int(11) NOT NULL COMMENT '帖子ID',
  `user_id` int(11) NOT NULL COMMENT '真实用户ID',
  `anonymous_name` varchar(50) NOT NULL COMMENT '匿名名称',
  `anonymous_avatar` varchar(255) NOT NULL COMMENT '匿名头像URL',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_message_user` (`message_id`, `user_id`),
  KEY `idx_message_id` (`message_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_anonymous_name` (`anonymous_name`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='匿名名称和头像映射表';

-- 6. 为message表添加索引
ALTER TABLE `message` ADD INDEX `idx_is_anonymous` (`is_anonymous`);

-- 7. 为comment表添加索引
ALTER TABLE `comment` ADD INDEX `idx_is_from_anonymous_post` (`is_from_anonymous_post`);

-- 8. 为post表添加索引
ALTER TABLE `post` ADD INDEX `idx_is_from_anonymous_post` (`is_from_anonymous_post`);

-- 示例数据（可选，用于测试）
-- INSERT INTO `message` (`user_id`, `username`, `face_url`, `content`, `send_timestamp`, `choose`, `is_anonymous`)
-- VALUES (1, '测试用户', 'https://www.bjgaoxiaoshequ.store/images/weixiao.png', '这是一条匿名测试帖子', UNIX_TIMESTAMP(), 2, 1);

-- 9. 为message表添加匿名头像字段（可选，如果需要在message表中也存储匿名头像）
-- ALTER TABLE `message`
-- ADD COLUMN `anonymous_avatar` varchar(255) DEFAULT NULL COMMENT '匿名头像URL' AFTER `is_anonymous`;
