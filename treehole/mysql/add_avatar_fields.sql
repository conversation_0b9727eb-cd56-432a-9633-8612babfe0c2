-- 为liaoran系统添加文字头像字段
-- 执行前请备份数据库

-- 为分类表添加文字头像字段
ALTER TABLE `liaoran_categories` 
ADD COLUMN `avatar_text` varchar(4) DEFAULT NULL COMMENT '头像文字（4个字）' AFTER `image_url`,
ADD COLUMN `avatar_color` varchar(20) DEFAULT NULL COMMENT '头像背景颜色' AFTER `avatar_text`;

-- 为对象表添加文字头像字段  
ALTER TABLE `liaoran_objects`
ADD COLUMN `avatar_text` varchar(4) DEFAULT NULL COMMENT '头像文字（4个字）' AFTER `image_url`,
ADD COLUMN `avatar_color` varchar(20) DEFAULT NULL COMMENT '头像背景颜色' AFTER `avatar_text`;

-- 将image_url字段改为可选
ALTER TABLE `liaoran_categories` 
MODIFY COLUMN `image_url` varchar(500) DEFAULT NULL COMMENT '分类图片URL';

ALTER TABLE `liaoran_objects`
MODIFY COLUMN `image_url` varchar(500) DEFAULT NULL COMMENT '对象图片URL';
