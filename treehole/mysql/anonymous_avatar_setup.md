# 匿名头像配置说明

## 1. 头像文件准备

### 文件要求：
- **数量**：100张头像图片
- **格式**：PNG 或 JPG 格式
- **尺寸**：建议 200x200 像素或更高
- **命名**：avatar_1.png, avatar_2.png, ..., avatar_100.png

### 建议的头像类型：
- 卡通动物头像
- 简约几何图形
- 抽象艺术图案
- 可爱表情符号
- 风景图标
- 植物图标

## 2. 服务器目录结构

```
/images/
├── anonymous/
│   ├── avatar_1.png
│   ├── avatar_2.png
│   ├── avatar_3.png
│   ├── ...
│   └── avatar_100.png
└── weixiao.png (默认头像)
```

## 3. 上传步骤

1. **创建目录**：
   ```bash
   mkdir -p /path/to/your/website/images/anonymous/
   ```

2. **上传头像文件**：
   - 将100张头像文件上传到 `/images/anonymous/` 目录
   - 确保文件命名正确：avatar_1.png 到 avatar_100.png

3. **设置权限**：
   ```bash
   chmod 644 /path/to/your/website/images/anonymous/*.png
   ```

## 4. 启用随机头像

修改 `treehole/app/utils/AnonymousUtil.php` 文件：

```php
// 找到 generateAnonymousAvatar 方法中的这两行：

// 注释掉默认头像
// return "https://www.bjgaoxiaoshequ.store/images/weixiao.png";

// 取消随机头像的注释
return "https://www.bjgaoxiaoshequ.store/images/anonymous/avatar_{$avatarNumber}.png";
```

## 5. 测试验证

1. **检查文件访问**：
   - 访问：`https://www.bjgaoxiaoshequ.store/images/anonymous/avatar_1.png`
   - 确保能正常显示头像

2. **测试匿名功能**：
   - 发布匿名帖子
   - 检查头像是否正确显示
   - 同一用户在同一帖子下头像应该一致

## 6. 头像生成逻辑

```php
// 头像选择算法：
$hash = md5($userId . '_' . $messageId);
$avatarNumber = hexdec(substr($hash, 0, 4)) % 100 + 1;
```

**特点**：
- 同一用户在同一帖子下始终显示相同头像
- 不同用户或不同帖子会显示不同头像
- 头像分布相对均匀

## 7. 备用方案

如果暂时没有100张头像，可以：

1. **使用较少的头像**：
   ```php
   // 修改为实际头像数量，比如20张
   $avatarNumber = hexdec(substr($hash, 0, 4)) % 20 + 1;
   ```

2. **继续使用默认头像**：
   ```php
   return "https://www.bjgaoxiaoshequ.store/images/weixiao.png";
   ```

## 8. 注意事项

- 确保所有头像文件都能正常访问
- 头像文件大小建议控制在50KB以内，提高加载速度
- 定期检查头像文件的可用性
- 可以根据需要调整头像数量（修改代码中的100为实际数量）
