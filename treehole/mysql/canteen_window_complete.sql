
-- 1. 食堂表
CREATE TABLE `canteens` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '食堂ID',
  `name` varchar(100) NOT NULL COMMENT '食堂名称',
  `description` text COMMENT '食堂描述',
  `location` varchar(200) DEFAULT NULL COMMENT '食堂位置',
  `image_url` varchar(500) DEFAULT NULL COMMENT '食堂图片',
  `floors` text COMMENT '楼层信息，JSON格式存储',
  `business_hours` varchar(200) DEFAULT NULL COMMENT '营业时间',
  `contact_phone` varchar(20) DEFAULT NULL COMMENT '联系电话',
  `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分',
  `total_ratings` int(11) DEFAULT 0 COMMENT '总评分数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-停用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食堂表';

-- 2. 窗口表
CREATE TABLE `windows` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '窗口ID',
  `canteen_id` int(11) NOT NULL COMMENT '所属食堂ID',
  `name` varchar(100) NOT NULL COMMENT '窗口名称',
  `floor` varchar(20) NOT NULL COMMENT '所在楼层',
  `window_number` varchar(20) DEFAULT NULL COMMENT '窗口编号',
  `description` text COMMENT '窗口描述',
  `image_url` varchar(500) DEFAULT NULL COMMENT '窗口图片',
  `specialty` varchar(200) DEFAULT NULL COMMENT '特色菜品',
  `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分',
  `total_ratings` int(11) DEFAULT 0 COMMENT '总评分数',
  `hot_comment` varchar(500) DEFAULT NULL COMMENT '热门评论',
  `business_hours` varchar(200) DEFAULT NULL COMMENT '营业时间',
  `created_by` int(11) DEFAULT NULL COMMENT '创建者用户ID',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-停用',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_canteen_id` (`canteen_id`),
  KEY `idx_floor` (`floor`),
  KEY `idx_status` (`status`),
  KEY `idx_created_by` (`created_by`),
  FOREIGN KEY (`canteen_id`) REFERENCES `canteens` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='窗口表';

-- 3. 食堂评分表
CREATE TABLE `canteen_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评分ID',
  `canteen_id` int(11) NOT NULL COMMENT '食堂ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `floor` varchar(20) DEFAULT NULL COMMENT '评分楼层',
  `rating` tinyint(2) NOT NULL COMMENT '评分：1-5分',
  `comment` text COMMENT '评论内容',
  `images` text COMMENT '图片URLs，JSON格式',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_canteen_id` (`canteen_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_floor` (`floor`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`canteen_id`) REFERENCES `canteens` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='食堂评分表';

-- 4. 窗口评分表（仅用于评分，不包含评论）
CREATE TABLE `window_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评分ID',
  `window_id` int(11) NOT NULL COMMENT '窗口ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `rating` smallint NOT NULL COMMENT '评分：-10到10分',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_window_user` (`window_id`,`user_id`),
  KEY `idx_window_id` (`window_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`window_id`) REFERENCES `windows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='窗口评分表';

-- 5. 窗口评论表（仅用于评论，不包含评分）
CREATE TABLE `window_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `window_id` int(11) NOT NULL COMMENT '窗口ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `images` text COMMENT '图片URLs，JSON格式',
  `parent_id` int(11) DEFAULT NULL COMMENT '父评论ID（用于回复）',
  `reply_to_user_id` int(11) DEFAULT NULL COMMENT '回复的用户ID',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_window_id` (`window_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`window_id`) REFERENCES `windows` (`id`) ON DELETE CASCADE,
  FOREIGN KEY (`parent_id`) REFERENCES `window_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='窗口评论表';

-- 6. 窗口评论点赞表
CREATE TABLE `window_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`,`user_id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`comment_id`) REFERENCES `window_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='窗口评论点赞表';

-- 7. 评分点赞表（保留用于食堂评分点赞）
CREATE TABLE `rating_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `rating_type` enum('canteen','window') NOT NULL COMMENT '评分类型',
  `rating_id` int(11) NOT NULL COMMENT '评分ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_rating_user` (`rating_type`,`rating_id`,`user_id`),
  KEY `idx_user_id` (`user_id`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='评分点赞表';

-- 8. 窗口推荐菜品表
CREATE TABLE `window_dish_recommendations` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '推荐ID',
  `window_id` int(11) NOT NULL COMMENT '窗口ID',
  `user_id` int(11) NOT NULL COMMENT '推荐用户ID',
  `dish_name` varchar(50) NOT NULL COMMENT '菜品名称',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_window_user_dish` (`window_id`,`user_id`,`dish_name`),
  KEY `idx_window_id` (`window_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_dish_name` (`dish_name`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`window_id`) REFERENCES `windows` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='窗口推荐菜品表';

-- 插入示例数据（可选）
-- 示例食堂数据
INSERT INTO `canteens` (`name`, `description`, `location`, `floors`, `business_hours`, `status`, `sort_order`) VALUES
('第一食堂', '学校主要食堂，提供各种美食', '校园中心区域', '["1F", "2F", "3F"]', '06:30-21:30', 1, 1),
('第二食堂', '新建食堂，环境优雅', '校园东区', '["1F", "2F"]', '07:00-21:00', 1, 2),
('第三食堂', '特色小食堂，主打地方菜', '校园西区', '["1F"]', '11:00-20:00', 1, 3);

-- 示例窗口数据
INSERT INTO `windows` (`canteen_id`, `name`, `floor`, `window_number`, `description`, `specialty`, `status`, `sort_order`) VALUES
(1, '川菜窗口', '1F', 'A01', '正宗川菜，麻辣鲜香', '麻婆豆腐,宫保鸡丁', 1, 1),
(1, '粤菜窗口', '1F', 'A02', '清淡粤菜，营养健康', '白切鸡,蒸蛋羹', 1, 2),
(1, '面食窗口', '2F', 'B01', '各种面条，汤面干面', '牛肉面,炸酱面', 1, 3),
(2, '西式快餐', '1F', 'C01', '汉堡薯条，西式简餐', '汉堡,薯条,可乐', 1, 1),
(3, '本地特色', '1F', 'D01', '当地特色菜品', '红烧肉,糖醋排骨', 1, 1);

-- 示例推荐菜品数据
INSERT INTO `window_dish_recommendations` (`window_id`, `user_id`, `dish_name`) VALUES
(1, 1, '麻婆豆腐'),
(1, 2, '麻婆豆腐'),
(1, 1, '宫保鸡丁'),
(1, 3, '回锅肉'),
(2, 2, '白切鸡'),
(2, 3, '白切鸡'),
(2, 1, '蒸蛋羹');
