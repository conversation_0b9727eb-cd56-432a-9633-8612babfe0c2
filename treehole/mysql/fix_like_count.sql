-- 修复窗口评论表的like_count字段
-- 确保所有现有记录的like_count字段不为NULL

-- 1. 检查当前状态
SELECT 'Before Fix:' as status;
SELECT
    id,
    content,
    like_count,
    (SELECT COUNT(*) FROM window_comment_likes WHERE comment_id = window_comments.id) as actual_likes
FROM window_comments
ORDER BY id;

-- 2. 更新所有NULL的like_count为0
UPDATE `window_comments` SET `like_count` = 0 WHERE `like_count` IS NULL;

-- 3. 确保字段有正确的默认值和NOT NULL约束
ALTER TABLE `window_comments` MODIFY COLUMN `like_count` int(11) NOT NULL DEFAULT 0 COMMENT '点赞数';

-- 4. 重新计算所有评论的点赞数（基于点赞表）
UPDATE `window_comments` wc
SET `like_count` = (
    SELECT COUNT(*)
    FROM `window_comment_likes` wcl
    WHERE wcl.`comment_id` = wc.`id`
);

-- 5. 显示修复后的结果
SELECT 'After Fix:' as status;
SELECT
    id,
    content,
    like_count,
    (SELECT COUNT(*) FROM window_comment_likes WHERE comment_id = window_comments.id) as actual_likes
FROM window_comments
ORDER BY id;

-- 6. 验证数据一致性
SELECT 'Data Consistency Check:' as status;
SELECT
    wc.id,
    wc.like_count as stored_count,
    COUNT(wcl.id) as actual_count,
    CASE
        WHEN wc.like_count = COUNT(wcl.id) THEN 'OK'
        ELSE 'MISMATCH'
    END as status
FROM window_comments wc
LEFT JOIN window_comment_likes wcl ON wc.id = wcl.comment_id
GROUP BY wc.id, wc.like_count
ORDER BY wc.id;
