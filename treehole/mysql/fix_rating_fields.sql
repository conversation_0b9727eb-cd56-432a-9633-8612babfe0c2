-- 修复评分相关字段的数据类型
-- 解决 avg_rating 字段数值超出范围的问题

-- 1. 修改 windows 表的 avg_rating 字段
-- 从 decimal(3,2) 改为 decimal(4,1) 以支持 -10.0 到 10.0 的范围
ALTER TABLE `windows` MODIFY COLUMN `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分';

-- 2. 修改 window_ratings 表的 rating 字段
-- 从 tinyint(2) 改为 smallint 以确保能存储 -10 到 10 的范围
ALTER TABLE `window_ratings` MODIFY COLUMN `rating` smallint NOT NULL COMMENT '评分：-10到10分';

-- 3. 修改 canteens 表的 avg_rating 字段（如果存在）
-- 检查 canteens 表是否有 avg_rating 字段，如果有也需要修改
-- ALTER TABLE `canteens` MODIFY COLUMN `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分';

-- 4. 验证修改结果
DESCRIBE `windows`;
DESCRIBE `window_ratings`;

-- 5. 测试数据插入（可选）
-- 测试是否能正确存储 -10.0 到 10.0 的评分
-- INSERT INTO `window_ratings` (`window_id`, `user_id`, `rating`) VALUES (1, 1, -10);
-- INSERT INTO `window_ratings` (`window_id`, `user_id`, `rating`) VALUES (1, 2, 10);

-- 6. 重新计算现有窗口的平均评分
-- 更新所有窗口的平均评分，确保使用新的字段类型
UPDATE `windows` w SET `avg_rating` = (
    SELECT COALESCE(ROUND(AVG(wr.rating), 1), 0.0)
    FROM `window_ratings` wr 
    WHERE wr.window_id = w.id AND wr.status = 1
);

-- 7. 显示修复后的结果
SELECT 'Database fields fixed successfully!' as status;
SELECT 
    w.id,
    w.name,
    w.avg_rating,
    w.total_ratings,
    COUNT(wr.id) as actual_ratings
FROM windows w
LEFT JOIN window_ratings wr ON w.id = wr.window_id AND wr.status = 1
GROUP BY w.id, w.name, w.avg_rating, w.total_ratings
ORDER BY w.id;
