-- 了然几分万能评分系统数据库表

-- 检查并删除已存在的表（如果需要重新创建）
-- DROP TABLE IF EXISTS `liaoran_comment_likes`;
-- DROP TABLE IF EXISTS `liaoran_comments`;
-- DROP TABLE IF EXISTS `liaoran_ratings`;
-- DROP TABLE IF EXISTS `liaoran_objects`;

-- 1. 评分对象表
CREATE TABLE `liaoran_objects` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '对象ID',
  `name` varchar(100) NOT NULL COMMENT '对象名称',
  `image_url` varchar(500) DEFAULT '/images/liaoran.png' COMMENT '对象图片',
  `created_by` int(11) NOT NULL COMMENT '创建者用户ID',
  `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分',
  `total_ratings` int(11) DEFAULT 0 COMMENT '总评分数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_avg_rating` (`avg_rating`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评分对象表';

-- 2. 评分表
CREATE TABLE `liaoran_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评分ID',
  `object_id` int(11) NOT NULL COMMENT '评分对象ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `rating` smallint NOT NULL COMMENT '评分：-10到10分',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_object_user` (`object_id`,`user_id`),
  KEY `idx_object_id` (`object_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`object_id`) REFERENCES `liaoran_objects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评分表';

-- 3. 评论表
CREATE TABLE `liaoran_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `object_id` int(11) NOT NULL COMMENT '评分对象ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text COMMENT '评论内容',
  `images` text COMMENT '图片URLs，JSON格式',
  `parent_id` int(11) DEFAULT 0 COMMENT '父评论ID，0表示主评论',
  `reply_to_user_id` int(11) DEFAULT 0 COMMENT '回复的用户ID',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_object_id` (`object_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_like_count` (`like_count`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`object_id`) REFERENCES `liaoran_objects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评论表';

-- 4. 评论点赞表
CREATE TABLE `liaoran_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_comment_user` (`comment_id`,`user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`comment_id`) REFERENCES `liaoran_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评论点赞表';

-- 示例数据
INSERT INTO `liaoran_objects` (`name`, `image_url`, `created_by`) VALUES
('北航沙河校区', '/images/liaoran.png', 1),
('学一食堂', '/images/shitang.png', 1),
('图书馆', '/images/liaoran.png', 1),
('宿舍楼', '/images/liaoran.png', 1),
('体育馆', '/images/liaoran.png', 1);

-- 示例评分数据
INSERT INTO `liaoran_ratings` (`object_id`, `user_id`, `rating`) VALUES
(1, 1, 8),
(1, 2, 7),
(1, 3, 9),
(2, 1, 6),
(2, 2, 5),
(3, 1, 9),
(3, 2, 8),
(4, 1, 7),
(5, 1, 8);

-- 示例评论数据
INSERT INTO `liaoran_comments` (`object_id`, `user_id`, `content`, `parent_id`) VALUES
(1, 1, '沙河校区环境很好，空气清新，适合学习生活。', 0),
(1, 2, '同意楼上，而且交通也比较方便。', 1),
(2, 1, '学一食堂菜品丰富，价格实惠，就是人有点多。', 0),
(3, 1, '图书馆座位充足，学习氛围很好，推荐！', 0);

-- 更新对象平均评分（触发器或定时任务可以自动更新）
UPDATE `liaoran_objects` o SET
  `avg_rating` = (
    SELECT ROUND(AVG(r.rating), 1)
    FROM `liaoran_ratings` r
    WHERE r.object_id = o.id AND r.status = 1
  ),
  `total_ratings` = (
    SELECT COUNT(*)
    FROM `liaoran_ratings` r
    WHERE r.object_id = o.id AND r.status = 1
  )
WHERE o.status = 1;
