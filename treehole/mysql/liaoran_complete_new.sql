-- 了然几分系统完整建表脚本（三级结构）
-- 删除已存在的表（如果需要重新创建）
DROP TABLE IF EXISTS `liaoran_comment_likes`;
DROP TABLE IF EXISTS `liaoran_comments`;
DROP TABLE IF EXISTS `liaoran_ratings`;
DROP TABLE IF EXISTS `liaoran_objects`;
DROP TABLE IF EXISTS `liaoran_categories`;

-- 1. 分类表（第一级）
CREATE TABLE `liaoran_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `image_url` varchar(500) DEFAULT NULL COMMENT '分类图片URL',
  `avatar_text` varchar(4) DEFAULT NULL COMMENT '头像文字（4个字）',
  `avatar_color` varchar(20) DEFAULT NULL COMMENT '头像背景颜色',
  `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分',
  `total_ratings` int(11) DEFAULT 0 COMMENT '总评分数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `created_by` int(11) NOT NULL COMMENT '创建者用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_avg_rating` (`avg_rating`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分分类表';

-- 2. 评分对象表（第二级）
CREATE TABLE `liaoran_objects` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '对象ID',
  `category_id` int(11) NOT NULL COMMENT '所属分类ID',
  `name` varchar(100) NOT NULL COMMENT '对象名称',
  `description` text COMMENT '对象描述',
  `image_url` varchar(500) DEFAULT NULL COMMENT '对象图片URL',
  `avatar_text` varchar(4) DEFAULT NULL COMMENT '头像文字（4个字）',
  `avatar_color` varchar(20) DEFAULT NULL COMMENT '头像背景颜色',
  `created_by` int(11) NOT NULL COMMENT '创建者用户ID',
  `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分',
  `total_ratings` int(11) DEFAULT 0 COMMENT '总评分数',
  `hot_comment` varchar(500) DEFAULT NULL COMMENT '热门评论',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_category_id` (`category_id`),
  KEY `idx_name` (`name`),
  KEY `idx_created_by` (`created_by`),
  KEY `idx_avg_rating` (`avg_rating`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`category_id`) REFERENCES `liaoran_categories` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评分对象表';

-- 3. 评分表
CREATE TABLE `liaoran_ratings` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评分ID',
  `object_id` int(11) NOT NULL COMMENT '评分对象ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `rating` int(11) NOT NULL COMMENT '评分（-10到10）',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_object` (`object_id`, `user_id`),
  KEY `idx_object_id` (`object_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_rating` (`rating`),
  KEY `idx_status` (`status`),
  FOREIGN KEY (`object_id`) REFERENCES `liaoran_objects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评分表';

-- 4. 评论表
CREATE TABLE `liaoran_comments` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '评论ID',
  `object_id` int(11) NOT NULL COMMENT '评分对象ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `content` text NOT NULL COMMENT '评论内容',
  `images` text COMMENT '评论图片（JSON格式）',
  `parent_id` int(11) DEFAULT 0 COMMENT '父评论ID（0表示主评论）',
  `reply_to_user_id` int(11) DEFAULT 0 COMMENT '回复的用户ID',
  `like_count` int(11) DEFAULT 0 COMMENT '点赞数',
  `reply_count` int(11) DEFAULT 0 COMMENT '回复数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_object_id` (`object_id`),
  KEY `idx_user_id` (`user_id`),
  KEY `idx_parent_id` (`parent_id`),
  KEY `idx_reply_to_user_id` (`reply_to_user_id`),
  KEY `idx_like_count` (`like_count`),
  KEY `idx_status` (`status`),
  KEY `idx_created_at` (`created_at`),
  FOREIGN KEY (`object_id`) REFERENCES `liaoran_objects` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评论表';

-- 5. 评论点赞表
CREATE TABLE `liaoran_comment_likes` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '点赞ID',
  `comment_id` int(11) NOT NULL COMMENT '评论ID',
  `user_id` int(11) NOT NULL COMMENT '用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `unique_user_comment` (`comment_id`, `user_id`),
  KEY `idx_comment_id` (`comment_id`),
  KEY `idx_user_id` (`user_id`),
  FOREIGN KEY (`comment_id`) REFERENCES `liaoran_comments` (`id`) ON DELETE CASCADE
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分评论点赞表';

-- 插入示例分类数据
INSERT INTO `liaoran_categories` (`name`, `description`, `image_url`, `created_by`, `sort_order`) VALUES
('校园设施', '校园内的各种基础设施和建筑', '/images/liaoran.png', 1, 1),
('餐饮美食', '校园内的食堂、餐厅、小吃等', '/images/shitang.png', 1, 2),
('学习工具', '学习相关的软件、设备、平台等', '/images/liaoran.png', 1, 3),
('生活服务', '校园生活相关的各种服务', '/images/liaoran.png', 1, 4),
('娱乐休闲', '娱乐、运动、休闲相关设施', '/images/liaoran.png', 1, 5);

-- 插入示例对象数据
INSERT INTO `liaoran_objects` (`category_id`, `name`, `description`, `image_url`, `created_by`) VALUES
(1, '北航沙河校区', '北京航空航天大学沙河校区，环境优美，设施完善', '/images/liaoran.png', 1),
(2, '学一食堂', '沙河校区第一食堂，菜品丰富，价格实惠', '/images/shitang.png', 1),
(1, '图书馆', '沙河校区图书馆，学习氛围浓厚，座位充足', '/images/liaoran.png', 1),
(1, '宿舍楼', '学生宿舍，住宿环境和管理服务', '/images/liaoran.png', 1),
(5, '体育馆', '校园体育馆，运动设施和环境', '/images/liaoran.png', 1),
(3, '微信小程序开发工具', '微信官方开发工具，用于小程序开发', '/images/liaoran.png', 1),
(4, '校园网', '校园网络服务，网速和稳定性', '/images/liaoran.png', 1),
(2, '学二食堂', '沙河校区第二食堂', '/images/shitang.png', 1),
(2, '咖啡厅', '校园内的咖啡厅，环境和服务', '/images/liaoran.png', 1),
(3, '实验室', '各种实验室设施和环境', '/images/liaoran.png', 1);

-- 插入示例评分数据
INSERT INTO `liaoran_ratings` (`object_id`, `user_id`, `rating`) VALUES
(1, 1, 8),
(1, 2, 7),
(1, 3, 9),
(2, 1, 6),
(2, 2, 5),
(3, 1, 9),
(3, 2, 8),
(4, 1, 7),
(5, 1, 8);

-- 插入示例评论数据
INSERT INTO `liaoran_comments` (`object_id`, `user_id`, `content`, `parent_id`) VALUES
(1, 1, '沙河校区环境很好，空气清新，适合学习生活。', 0),
(1, 2, '同意楼上，而且交通也比较方便。', 1),
(2, 1, '学一食堂菜品丰富，价格实惠，就是人有点多。', 0),
(3, 1, '图书馆座位充足，学习氛围很好，推荐！', 0);

-- 更新分类的平均评分和评分数（基于该分类下所有对象的数据）
UPDATE `liaoran_categories` c SET
  `avg_rating` = (
    SELECT ROUND(AVG(o.avg_rating), 1)
    FROM `liaoran_objects` o
    WHERE o.category_id = c.id AND o.status = 1
  ),
  `total_ratings` = (
    SELECT SUM(o.total_ratings)
    FROM `liaoran_objects` o
    WHERE o.category_id = c.id AND o.status = 1
  )
WHERE c.status = 1;

-- 更新对象的平均评分和评分数
UPDATE `liaoran_objects` o SET
  `avg_rating` = (
    SELECT ROUND(AVG(r.rating), 1)
    FROM `liaoran_ratings` r
    WHERE r.object_id = o.id AND r.status = 1
  ),
  `total_ratings` = (
    SELECT COUNT(*)
    FROM `liaoran_ratings` r
    WHERE r.object_id = o.id AND r.status = 1
  )
WHERE o.status = 1;

-- 更新评论的点赞数
UPDATE `liaoran_comments` c SET
  `like_count` = (
    SELECT COUNT(*)
    FROM `liaoran_comment_likes` l
    WHERE l.comment_id = c.id
  )
WHERE c.status = 1;
