-- 了然几分系统三级结构数据库更新脚本
-- 将现有的两级结构改为三级结构（类似canteen系统）

-- 1. 创建分类表（类似canteens表）
CREATE TABLE IF NOT EXISTS `liaoran_categories` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT '分类ID',
  `name` varchar(100) NOT NULL COMMENT '分类名称',
  `description` text COMMENT '分类描述',
  `image_url` varchar(500) DEFAULT '/images/liaoran.png' COMMENT '分类图片',
  `avg_rating` decimal(4,1) DEFAULT 0.0 COMMENT '平均评分',
  `total_ratings` int(11) DEFAULT 0 COMMENT '总评分数',
  `status` tinyint(1) DEFAULT 1 COMMENT '状态：1-正常，0-删除',
  `sort_order` int(11) DEFAULT 0 COMMENT '排序权重',
  `created_by` int(11) NOT NULL COMMENT '创建者用户ID',
  `created_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP COMMENT '创建时间',
  `updated_at` timestamp NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '更新时间',
  PRIMARY KEY (`id`),
  KEY `idx_name` (`name`),
  KEY `idx_status` (`status`),
  KEY `idx_sort_order` (`sort_order`),
  KEY `idx_avg_rating` (`avg_rating`),
  KEY `idx_created_by` (`created_by`)
) ENGINE=InnoDB DEFAULT CHARSET=utf8mb4 COMMENT='了然几分分类表';

-- 2. 为现有的liaoran_objects表添加category_id字段
ALTER TABLE `liaoran_objects` 
ADD COLUMN IF NOT EXISTS `category_id` int(11) DEFAULT NULL COMMENT '所属分类ID' AFTER `id`,
ADD COLUMN IF NOT EXISTS `description` text COMMENT '对象描述' AFTER `name`,
ADD COLUMN IF NOT EXISTS `hot_comment` varchar(500) DEFAULT NULL COMMENT '热门评论' AFTER `total_ratings`,
ADD COLUMN IF NOT EXISTS `sort_order` int(11) DEFAULT 0 COMMENT '排序权重' AFTER `status`,
ADD INDEX IF NOT EXISTS `idx_category_id` (`category_id`);

-- 3. 添加外键约束（如果不存在）
-- 注意：只有在category_id字段存在且有对应的分类记录时才能添加外键
-- ALTER TABLE `liaoran_objects` ADD FOREIGN KEY (`category_id`) REFERENCES `liaoran_categories` (`id`) ON DELETE CASCADE;

-- 4. 插入示例分类数据
INSERT IGNORE INTO `liaoran_categories` (`name`, `description`, `image_url`, `created_by`, `sort_order`) VALUES
('校园设施', '校园内的各种基础设施和建筑', '/images/liaoran.png', 1, 1),
('餐饮美食', '校园内的食堂、餐厅、小吃等', '/images/shitang.png', 1, 2),
('学习工具', '学习相关的软件、设备、平台等', '/images/liaoran.png', 1, 3),
('生活服务', '校园生活相关的各种服务', '/images/liaoran.png', 1, 4),
('娱乐休闲', '娱乐、运动、休闲相关设施', '/images/liaoran.png', 1, 5);

-- 5. 更新现有对象数据，设置分类ID
-- 首先为没有分类的对象创建默认分类
INSERT IGNORE INTO `liaoran_categories` (`name`, `description`, `image_url`, `created_by`, `sort_order`) VALUES
('其他', '未分类的评分对象', '/images/liaoran.png', 1, 99);

-- 获取分类ID并更新对象
UPDATE `liaoran_objects` o 
SET `category_id` = (SELECT id FROM `liaoran_categories` WHERE name = '其他' LIMIT 1)
WHERE `category_id` IS NULL;

-- 6. 插入示例对象数据（如果表为空或数据较少）
INSERT IGNORE INTO `liaoran_objects` (`category_id`, `name`, `description`, `image_url`, `created_by`) VALUES
((SELECT id FROM `liaoran_categories` WHERE name = '校园设施' LIMIT 1), '北航沙河校区', '北京航空航天大学沙河校区，环境优美，设施完善', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '餐饮美食' LIMIT 1), '学一食堂', '沙河校区第一食堂，菜品丰富，价格实惠', '/images/shitang.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '校园设施' LIMIT 1), '图书馆', '沙河校区图书馆，学习氛围浓厚，座位充足', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '校园设施' LIMIT 1), '宿舍楼', '学生宿舍，住宿环境和管理服务', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '娱乐休闲' LIMIT 1), '体育馆', '校园体育馆，运动设施和环境', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '学习工具' LIMIT 1), '微信小程序开发工具', '微信官方开发工具，用于小程序开发', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '生活服务' LIMIT 1), '校园网', '校园网络服务，网速和稳定性', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '餐饮美食' LIMIT 1), '学二食堂', '沙河校区第二食堂', '/images/shitang.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '餐饮美食' LIMIT 1), '咖啡厅', '校园内的咖啡厅，环境和服务', '/images/liaoran.png', 1),
((SELECT id FROM `liaoran_categories` WHERE name = '学习工具' LIMIT 1), '实验室', '各种实验室设施和环境', '/images/liaoran.png', 1);

-- 7. 插入示例评分数据（如果评分表为空）
INSERT IGNORE INTO `liaoran_ratings` (`object_id`, `user_id`, `rating`) VALUES
(1, 1, 8),
(1, 2, 7),
(1, 3, 9),
(2, 1, 6),
(2, 2, 5),
(3, 1, 9),
(3, 2, 8),
(4, 1, 7),
(5, 1, 8);

-- 8. 插入示例评论数据（如果评论表为空）
INSERT IGNORE INTO `liaoran_comments` (`object_id`, `user_id`, `content`, `parent_id`) VALUES
(1, 1, '沙河校区环境很好，空气清新，适合学习生活。', 0),
(1, 2, '同意楼上，而且交通也比较方便。', 1),
(2, 1, '学一食堂菜品丰富，价格实惠，就是人有点多。', 0),
(3, 1, '图书馆座位充足，学习氛围很好，推荐！', 0);

-- 9. 更新分类的平均评分和评分数（基于该分类下所有对象的数据）
UPDATE `liaoran_categories` c SET
  `avg_rating` = (
    SELECT ROUND(AVG(o.avg_rating), 1)
    FROM `liaoran_objects` o
    WHERE o.category_id = c.id AND o.status = 1
  ),
  `total_ratings` = (
    SELECT SUM(o.total_ratings)
    FROM `liaoran_objects` o
    WHERE o.category_id = c.id AND o.status = 1
  )
WHERE c.status = 1;

-- 10. 更新对象的平均评分和评分数
UPDATE `liaoran_objects` o SET
  `avg_rating` = (
    SELECT ROUND(AVG(r.rating), 1)
    FROM `liaoran_ratings` r
    WHERE r.object_id = o.id AND r.status = 1
  ),
  `total_ratings` = (
    SELECT COUNT(*)
    FROM `liaoran_ratings` r
    WHERE r.object_id = o.id AND r.status = 1
  )
WHERE o.status = 1;
