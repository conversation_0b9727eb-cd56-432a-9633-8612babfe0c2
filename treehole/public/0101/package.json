{"name": "beauty-ai-assistant", "version": "1.0.0", "type": "module", "main": "server/app.js", "scripts": {"start": "node server/app.js", "dev": "nodemon server/app.js", "build": "echo 'Build complete'"}, "keywords": ["beauty", "ai", "assistant", "cosmetic", "analysis"], "author": "", "license": "ISC", "description": "美妆智能体交互原型", "dependencies": {"cors": "^2.8.5", "dashscope-node": "^0.2.2", "dotenv": "^16.5.0", "express": "^5.1.0", "multer": "^2.0.0"}, "devDependencies": {"nodemon": "^3.1.10"}}