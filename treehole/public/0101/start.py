#!/usr/bin/env python
"""
美妆内容审核智能体启动脚本
"""
import os
import sys
import webbrowser
import time
import subprocess
import platform

def check_dependencies():
    """检查依赖包是否已安装"""
    try:
        import flask
        import flask_cors
        import requests
        print("✅ 所有依赖包已安装")
        return True
    except ImportError as e:
        print(f"❌ 缺少依赖包: {e}")
        print("正在尝试安装依赖包...")
        try:
            if os.path.exists("requirements.txt"):
                subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
            else:
                subprocess.check_call([sys.executable, "-m", "pip", "install", "flask", "flask-cors", "requests"])
            print("✅ 依赖包安装成功")
            return True
        except Exception as e:
            print(f"❌ 依赖包安装失败: {e}")
            print("请手动安装依赖包: pip install flask flask-cors requests")
            return False

def check_api_key():
    """检查API密钥是否已配置"""
    with open("api_server.py", "r", encoding="utf-8") as f:
        content = f.read()
    
    if "API_KEY = \"sk-" in content:
        print("⚠️ 检测到默认API密钥，建议替换为您自己的API密钥")
        choice = input("是否继续使用默认密钥启动? (y/n): ")
        return choice.lower() == 'y'
    else:
        print("✅ API密钥已配置")
        return True

def start_server():
    """启动服务器"""
    print("正在启动服务器...")
    # 使用非阻塞方式启动服务器
    if platform.system() == "Windows":
        server_process = subprocess.Popen([sys.executable, "api_server.py"], 
                                         creationflags=subprocess.CREATE_NEW_CONSOLE)
    else:
        server_process = subprocess.Popen([sys.executable, "api_server.py"])
    
    # 等待服务器启动
    print("等待服务器启动...")
    time.sleep(2)
    
    # 打开浏览器
    print("正在打开浏览器...")
    webbrowser.open("http://127.0.0.1:8080")
    
    print("\n==================================================")
    print("🚀 美妆内容审核智能体已启动")
    print("📝 在浏览器中访问: http://127.0.0.1:8080")
    print("⚠️ 请保持此窗口开启，关闭窗口将停止服务器")
    print("==================================================\n")
    
    try:
        # 保持脚本运行，直到用户按Ctrl+C
        while True:
            time.sleep(1)
    except KeyboardInterrupt:
        print("\n正在关闭服务器...")
        server_process.terminate()
        print("服务器已关闭")

def main():
    """主函数"""
    print("\n==================================================")
    print("🌸 美妆内容审核智能体启动工具")
    print("==================================================\n")
    
    # 检查依赖
    if not check_dependencies():
        input("按Enter键退出...")
        return
    
    # 检查API密钥
    if not check_api_key():
        print("\n请在api_server.py文件中配置您的API密钥后再启动")
        input("按Enter键退出...")
        return
    
    # 启动服务器
    start_server()

if __name__ == "__main__":
    main() 