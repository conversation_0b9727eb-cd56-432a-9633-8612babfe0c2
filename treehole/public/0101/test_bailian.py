import os
from openai import OpenAI

# 阿里云百炼API配置
API_KEY = "sk-b4454b21ac434e5f858e0c71c13fe60d"

def test_bailian_api():
    try:
        # 初始化客户端
        client = OpenAI(
            api_key=API_KEY,
            base_url="https://dashscope.aliyuncs.com/compatible-mode/v1"
        )
        
        print("正在发送请求到阿里云百炼API...")
        
        # 发送请求
        completion = client.chat.completions.create(
            model="qwen-plus",  # 使用通义千问-Plus模型
            messages=[
                {"role": "system", "content": "You are a helpful assistant."},
                {"role": "user", "content": "你好，请介绍一下你自己。"}
            ]
        )
        
        # 输出结果
        print("API 调用成功！")
        print("响应内容：")
        print(completion.choices[0].message.content)
        return completion
        
    except Exception as e:
        print(f"调用过程中发生异常：{str(e)}")
        return None

if __name__ == "__main__":
    test_bailian_api() 