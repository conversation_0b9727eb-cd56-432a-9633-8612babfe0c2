import requests
import json

# API配置
API_URL = "http://127.0.0.1:8080/api/analyze-text"

def test_case_analysis():
    """测试赛题案例分析"""
    print("开始测试赛题案例分析...")
    
    # 赛题案例原始文本
    original_text = """生活需要妆，美丽不打烊！但凡精制的女生，都不会放弃自我管理。脸蛋只有一张，精制一点又何妨？用了这款特效面霜，我的肌肤就像剥了壳的鸡蛋一样立刻变白，色斑色块全部消失，仿佛皮肤吃了仙丹一样，一夜见效，完全无副作用，效果简直绝绝子！"""
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "text": original_text
    }
    
    try:
        print("发送文本到内容审核API...")
        response = requests.post(
            API_URL,
            headers=headers,
            json=data
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API调用成功!")
            
            # 分析结果
            if result.get("success"):
                data = result.get("data", {})
                
                print("\n==== 原始文本 ====")
                print(original_text)
                
                print("\n==== 错别字标注 ====")
                typos = []
                for error in data.get("errors", []):
                    if error.get("type") == "typo":
                        typos.append(f"{error.get('text')} -> {error.get('suggestion')}")
                if typos:
                    for typo in typos:
                        print(typo)
                else:
                    print("未检测到错别字")
                
                print("\n==== 违规内容标注 ====")
                violations = []
                for error in data.get("errors", []):
                    if error.get("type") in ["forbidden", "medical", "safety"]:
                        violations.append(f"{error.get('text')}: {error.get('reason')}")
                if violations:
                    for violation in violations:
                        print(violation)
                else:
                    print("未检测到违规内容")
                
                print("\n==== 夸大表述标注 ====")
                exaggerations = []
                for error in data.get("errors", []):
                    if error.get("type") == "exaggerated":
                        exaggerations.append(f"{error.get('text')}: {error.get('reason')}")
                if exaggerations:
                    for exaggeration in exaggerations:
                        print(exaggeration)
                else:
                    print("未检测到夸大表述")
                
                print("\n==== 修改后文案 ====")
                if data.get("improved_full_text"):
                    print(data.get("improved_full_text"))
                else:
                    print("未提供修改后文案")
                
                print("\n==== 合规评分 ====")
                compliance = data.get("compliance", {})
                print(f"评分: {compliance.get('score', 'N/A')}")
                print(f"主要问题: {', '.join(compliance.get('issues', ['无']))}")
                print(f"风险级别: {compliance.get('risk_level', 'N/A')}")
                
                return True
            else:
                print("API调用返回失败状态")
                return False
        else:
            print(f"API调用失败! 状态码: {response.status_code}")
            print("错误信息:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 确保本地API服务器已启动
    print("请确保本地API服务器已启动 (python api_server.py)")
    print("测试将在3秒后开始...")
    import time
    time.sleep(3)
    test_case_analysis() 