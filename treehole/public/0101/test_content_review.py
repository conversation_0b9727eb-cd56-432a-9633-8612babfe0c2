import requests
import json

# API配置
API_URL = "http://127.0.0.1:8080/api/analyze-text"

def test_content_review():
    """测试美妆内容审核功能"""
    print("开始测试美妆内容审核功能...")
    
    # 案例中的原始文本
    original_text = """生活需要妆，美丽不打烊！但凡精制的女生，都不会放弃自我管理。脸蛋只有一张，精制一点又何妨？用了这款特效面霜，我的肌肤就像剥了壳的鸡蛋一样立刻变白，色斑色块全部消失，仿佛皮肤吃了仙丹一样，一夜见效，完全无副作用，效果简直绝绝子！"""
    
    headers = {
        "Content-Type": "application/json"
    }
    
    data = {
        "text": original_text
    }
    
    try:
        print("发送文本到内容审核API...")
        response = requests.post(
            API_URL,
            headers=headers,
            json=data
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API调用成功!")
            print("审核结果:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            
            # 分析结果
            if result.get("success"):
                data = result.get("data", {})
                errors = data.get("errors", [])
                suggestions = data.get("suggestions", [])
                compliance = data.get("compliance", {})
                
                print("\n==== 审核结果分析 ====")
                print(f"发现问题数量: {len(errors)}")
                for i, error in enumerate(errors):
                    print(f"问题 {i+1}: {error.get('text')} - {error.get('reason')}")
                
                print(f"\n改进建议数量: {len(suggestions)}")
                for i, suggestion in enumerate(suggestions):
                    print(f"建议 {i+1}: {suggestion.get('original')} -> {suggestion.get('improved')}")
                
                print(f"\n合规评分: {compliance.get('score', 0)}")
                print(f"主要问题: {', '.join(compliance.get('issues', []))}")
            
            return True
        else:
            print(f"API调用失败! 状态码: {response.status_code}")
            print("错误信息:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    # 确保本地API服务器已启动
    print("请确保本地API服务器已启动 (python api_server.py)")
    print("测试将在3秒后开始...")
    import time
    time.sleep(3)
    test_content_review() 