import requests
import json

# 阿里云百炼API配置
API_KEY = "sk-b4454b21ac434e5f858e0c71c13fe60d"
API_BASE_URL = "https://dashscope.aliyuncs.com/compatible-mode/v1/chat/completions"

def test_dashscope_api():
    """测试阿里云百炼API连接"""
    print("开始测试阿里云百炼API连接...")
    
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    data = {
        "model": "qwen-plus",
        "messages": [
            {"role": "system", "content": "你是一个助手"},
            {"role": "user", "content": "你好，请做个简单回复测试连接"}
        ]
    }
    
    try:
        print("发送请求到阿里云百炼API...")
        response = requests.post(
            API_BASE_URL,
            headers=headers,
            json=data
        )
        
        print(f"状态码: {response.status_code}")
        
        if response.status_code == 200:
            result = response.json()
            print("API连接成功!")
            print("响应内容:")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return True
        else:
            print(f"API连接失败! 状态码: {response.status_code}")
            print("错误信息:")
            print(response.text)
            return False
            
    except Exception as e:
        print(f"发生错误: {str(e)}")
        return False

if __name__ == "__main__":
    test_dashscope_api() 