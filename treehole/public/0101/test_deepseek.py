import os
import requests
import json

# Deepseek API 配置
API_KEY = "***********************************"
API_BASE = "https://api.deepseek.com/v1/chat/completions"

def test_deepseek_api(model="deepseek-chat"):
    """
    测试 Deepseek API 连接
    
    参数:
        model (str): 要使用的模型名称，可选值包括 "deepseek-chat" 或 "deepseek-reasoner" (DeepSeek-R1)
    """
    # 设置请求头 - 使用 Bearer 认证方式
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 设置请求数据 - 符合 chat completions 格式
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "你好，请介绍一下你自己。"}
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    try:
        # 发送请求
        print(f"正在使用 {model} 模型发送请求到 Deepseek API...")
        response = requests.post(
            API_BASE,
            headers=headers,
            json=data
        )
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            print("API 调用成功！")
            print("响应内容：")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
        else:
            print(f"API 调用失败，状态码：{response.status_code}")
            print("错误信息：", response.text)
            return None
            
    except Exception as e:
        print(f"发生错误：{str(e)}")
        return None

if __name__ == "__main__":
    # 先尝试使用标准聊天模型
    print("===== 测试 deepseek-chat 模型 =====")
    test_deepseek_api("deepseek-chat")
    
    # 再尝试使用推理模型
    print("\n===== 测试 deepseek-reasoner 模型 =====")
    test_deepseek_api("deepseek-reasoner") 