import os
import requests
import json

# OpenAI API 配置
API_KEY = "sk-soecktigdnauahophntxpjxkoulwqltlpspnpiyfmrxtxgcj"
API_BASE = "https://api.openai.com/v1/chat/completions"

def test_openai_api(model="gpt-3.5-turbo"):
    """
    测试 OpenAI API 连接
    
    参数:
        model (str): 要使用的模型名称，如 "gpt-3.5-turbo" 或 "gpt-4"
    """
    # 设置请求头 - 使用 Bearer 认证方式
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json"
    }
    
    # 设置请求数据
    data = {
        "model": model,
        "messages": [
            {"role": "user", "content": "你好，请介绍一下你自己。"}
        ],
        "temperature": 0.7,
        "max_tokens": 1000
    }
    
    try:
        # 发送请求
        print(f"正在使用 {model} 模型发送请求到 OpenAI API...")
        response = requests.post(
            API_BASE,
            headers=headers,
            json=data
        )
        
        # 检查响应状态
        if response.status_code == 200:
            result = response.json()
            print("API 调用成功！")
            print("响应内容：")
            print(json.dumps(result, ensure_ascii=False, indent=2))
            return result
        else:
            print(f"API 调用失败，状态码：{response.status_code}")
            print("错误信息：", response.text)
            return None
            
    except Exception as e:
        print(f"发生错误：{str(e)}")
        return None

if __name__ == "__main__":
    # 尝试使用 GPT-3.5-Turbo 模型
    print("===== 测试 GPT-3.5-Turbo 模型 =====")
    test_openai_api("gpt-3.5-turbo") 