import os
from openai import OpenAI
import json

# OpenAI API 配置
API_KEY = "sk-soecktigdnauahophntxpjxkoulwqltlpspnpiyfmrxtxgcj"

def test_openai_api(model="gpt-3.5-turbo"):
    """
    使用 OpenAI 官方库测试 API 连接
    
    参数:
        model (str): 要使用的模型名称，如 "gpt-3.5-turbo" 或 "gpt-4"
    """
    try:
        # 初始化 OpenAI 客户端
        print(f"正在初始化 OpenAI 客户端并使用 {model} 模型...")
        client = OpenAI(api_key=API_KEY)
        
        # 发送请求
        print("发送请求到 OpenAI API...")
        response = client.chat.completions.create(
            model=model,
            messages=[
                {"role": "user", "content": "你好，请介绍一下你自己。"}
            ],
            temperature=0.7,
            max_tokens=1000
        )
        
        # 处理响应
        print("API 调用成功！")
        print("响应内容：")
        print(response.choices[0].message.content)
        return response
            
    except Exception as e:
        print(f"发生错误：{str(e)}")
        return None

if __name__ == "__main__":
    # 尝试使用 GPT-3.5-Turbo 模型
    print("===== 使用 OpenAI 官方库测试 GPT-3.5-Turbo 模型 =====")
    test_openai_api("gpt-3.5-turbo") 