<?php
// 测试数据库连接和表是否存在
require_once '../vendor/autoload.php';

use think\facade\Db;
use think\facade\Config;

// 加载配置
Config::load('../config/database.php', 'database');

try {
    // 测试数据库连接
    $result = Db::query('SELECT 1');
    echo "数据库连接成功\n";
    
    // 检查canteens表是否存在
    $tables = Db::query("SHOW TABLES LIKE 'canteens'");
    if (empty($tables)) {
        echo "canteens表不存在，请先执行建表SQL\n";
    } else {
        echo "canteens表存在\n";
        
        // 查看表结构
        $structure = Db::query("DESCRIBE canteens");
        echo "表结构：\n";
        foreach ($structure as $field) {
            echo "- {$field['Field']}: {$field['Type']}\n";
        }
    }
    
} catch (Exception $e) {
    echo "数据库错误: " . $e->getMessage() . "\n";
}
?>
