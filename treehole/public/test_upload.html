<!DOCTYPE html>
<html>
<head>
    <title>测试头像上传</title>
    <meta charset="utf-8">
</head>
<body>
    <h2>测试食堂头像上传</h2>
    <form action="/treehole/public/index.php/upload/uploadCanteenAvatar" method="post" enctype="multipart/form-data">
        <p>
            <label>选择头像:</label>
            <input type="file" name="avatar" accept="image/*" required>
        </p>
        <p>
            <label>Token:</label>
            <input type="text" name="token" value="test_token" style="width: 300px;">
        </p>
        <p>
            <input type="submit" value="上传">
        </p>
    </form>
    
    <h3>说明:</h3>
    <ul>
        <li>文件会保存到: treehole/public/uploads/canteen/</li>
        <li>返回的URL格式: /uploads/canteen/canteen_时间戳_随机数.扩展名</li>
        <li>支持的格式: jpg, jpeg, png</li>
        <li>最大文件大小: 5MB</li>
    </ul>
</body>
</html>
