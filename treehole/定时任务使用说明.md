# 新定时任务系统使用说明

## 概述

为了解决原有 EasyTask 定时任务不稳定的问题，我创建了一个新的定时任务系统，包含以下三个核心组件：

1. **MessagePushTask.php** - 消息推送任务执行器
2. **TaskScheduler.php** - 任务调度器
3. **TaskManager.php** - 任务管理器

## 新系统的优势

1. **精确的时间控制**：使用缓存记录上次执行时间，确保严格按照15分钟间隔执行
2. **更好的错误处理**：完善的异常捕获和日志记录
3. **状态监控**：可以实时查看任务运行状态
4. **优雅的启停**：支持平滑启动和停止
5. **批次处理优化**：避免频繁请求，增加批次间隔

## 使用方法

### 1. 启动定时任务

```bash
# 进入后端目录
cd treehole

# 启动任务调度器
php think task:manager start
```

### 2. 停止定时任务

```bash
# 停止任务调度器
php think task:manager stop
```

### 3. 查看任务状态

```bash
# 查看当前状态
php think task:manager status
```

### 4. 重启任务

```bash
# 重启任务调度器
php think task:manager restart
```

### 5. 手动执行一次推送

```bash
# 手动执行推送任务（不受15分钟限制）
php think message:push
```

## 状态说明

执行 `php think task:manager status` 后会显示：

- **调度器状态**：运行中/已停止
- **上次推送时间**：最后一次执行推送的时间
- **下次推送时间**：预计下次执行的时间
- **距离下次推送**：倒计时
- **相关进程数量**：系统中相关进程的数量

## 工作原理

1. **TaskScheduler** 作为主调度器，每分钟检查一次是否需要执行推送任务
2. **MessagePushTask** 负责具体的消息推送逻辑，包括：
   - 查询需要推送的消息（ispull=1）
   - 格式化消息内容
   - 分批发送到推送接口
   - 更新消息状态（ispull=0）
3. **TaskManager** 提供便捷的管理接口

## 时间控制机制

- 使用缓存记录上次执行时间
- 每次执行前检查是否满足15分钟间隔
- 只有满足间隔要求才会执行推送
- 避免了 EasyTask 可能的时间不准确问题

## 日志记录

所有操作都会记录到 ThinkPHP 的日志系统中，可以通过以下路径查看：

```
treehole/runtime/log/[年月]/[日期].log
```

## 故障排除

### 1. 如果任务没有按时执行

```bash
# 检查状态
php think task:manager status

# 如果显示已停止，重新启动
php think task:manager start
```

### 2. 如果推送失败

```bash
# 查看日志文件
tail -f treehole/runtime/log/$(date +%Y%m)/$(date +%d).log

# 手动执行一次测试
php think message:push
```

### 3. 如果进程卡死

```bash
# 强制停止
php think task:manager stop

# 等待几秒后重启
php think task:manager start
```

## 迁移说明

### 停用旧系统

1. 停止原有的 EasyTask：
```bash
php think task stop
```

2. 启动新系统：
```bash
php think task:manager start
```

### 数据兼容性

新系统完全兼容现有的数据库结构，不需要修改任何数据库字段。

## 监控建议

建议设置系统监控，定期检查任务状态：

```bash
# 可以添加到 crontab 中，每小时检查一次
0 * * * * cd /path/to/treehole && php think task:manager status
```

## 注意事项

1. 新系统启动后，请停用原有的 EasyTask 系统，避免冲突
2. 首次启动可能需要等待最多15分钟才会执行第一次推送
3. 如果需要立即推送，可以手动执行 `php think message:push`
4. 系统会自动处理网络错误和重试机制
